// api/host/session-lock.js - FIXED with direct subdomain detection
import { query } from '../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'PATCH') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        const { session_id, is_locked, locked_by } = req.body;
        
        if (!session_id || typeof is_locked !== 'boolean') {
            return res.status(400).json({
                success: false,
                error: 'session_id and is_locked (boolean) are required'
            });
        }

        // Direct subdomain extraction (consistent with working APIs)
        const host = req.headers.host || '';
        const winerySlug = host.split('.')[0];
        
        if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
            return res.status(400).json({
                success: false,
                error: 'Invalid winery subdomain'
            });
        }

        // Get winery by slug
        const wineryResult = await query(
            'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
            [winerySlug]
        );

        if (!wineryResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: `Winery "${winerySlug}" not found`
            });
        }

        const winery = wineryResult.rows[0];

        // Verify the session belongs to this winery
        const verifyQuery = `
            SELECT id, group_name, group_code
            FROM tasting_sessions
            WHERE id = $1 AND winery_id = $2
        `;

        const verifyResult = await query(verifyQuery, [session_id, winery.id]);

        if (verifyResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Session not found for this winery'
            });
        }

        const session = verifyResult.rows[0];

        // Update or insert session lock
        if (is_locked) {
            // Lock the session
            const lockQuery = `
                INSERT INTO session_locks (session_id, is_locked, locked_by, locked_at)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (session_id) 
                DO UPDATE SET 
                    is_locked = $2,
                    locked_by = $3,
                    locked_at = $4,
                    unlocked_at = NULL
                RETURNING *
            `;

            const lockValues = [
                session_id,
                true,
                locked_by || 'Host',
                new Date()
            ];

            const lockResult = await query(lockQuery, lockValues);

            res.status(200).json({
                success: true,
                message: 'Session locked successfully',
                session: {
                    id: session_id,
                    groupName: session.group_name,
                    groupCode: session.group_code,
                    isLocked: true,
                    lockedBy: locked_by || 'Host',
                    lockedAt: new Date()
                }
            });

        } else {
            // Unlock the session
            const unlockQuery = `
                UPDATE session_locks 
                SET is_locked = false,
                    unlocked_at = $1
                WHERE session_id = $2
                RETURNING *
            `;

            const unlockResult = await query(unlockQuery, [new Date(), session_id]);

            res.status(200).json({
                success: true,
                message: 'Session unlocked successfully',
                session: {
                    id: session_id,
                    groupName: session.group_name,
                    groupCode: session.group_code,
                    isLocked: false,
                    unlockedAt: new Date()
                }
            });
        }

    } catch (error) {
        console.error('Session lock update error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update session lock'
        });
    }
}