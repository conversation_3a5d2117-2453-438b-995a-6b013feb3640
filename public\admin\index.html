<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wine Admin Dashboard</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .admin-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #722f37;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .auth-badge {
            background: #28a745;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #c82333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .winery-selector {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .selector-title {
            font-size: 1.2rem;
            color: #722f37;
            font-weight: 600;
        }
        
        .winery-count {
            background: #722f37;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        
        .current-selection {
            background: linear-gradient(135deg, #722f37, #8b3a42);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .current-winery {
            flex: 1;
        }
        
        .current-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        
        .current-meta {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .change-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }
        
        .change-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .winery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .winery-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .winery-card:hover {
            border-color: #722f37;
            transform: translateY(-2px);
        }
        
        .winery-card.active {
            border-color: #722f37;
            background: #f8f9fa;
        }
        
        .winery-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .winery-meta {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .winery-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: #888;
        }
        
        .admin-panels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .panel:hover {
            transform: translateY(-5px);
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .panel-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .wines-icon {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .plateaux-icon {
            background: linear-gradient(135deg, #f9ca24, #f0932b);
            color: white;
        }
        
        .settings-icon {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
        }
        
        .sessions-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }
        
        .panel-subtitle {
            font-size: 0.9rem;
            color: #666;
        }
        
        .quick-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat {
            flex: 1;
            text-align: center;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #722f37;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.2rem;
        }
        
        .panel-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            flex: 1;
            padding: 0.8rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }
        
        .btn-primary {
            background: #722f37;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a252a;
        }
        
        .btn-secondary {
            background: #e9ecef;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #dee2e6;
        }
        
        .no-winery {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-style: italic;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #722f37;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .admin-panels {
                grid-template-columns: 1fr;
            }
            
            .admin-header {
                padding: 1rem;
            }
            
            .winery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="admin-logo">
            <span>🍷</span>
            Wine Admin Portal
        </div>
        <div class="user-info">
            <span class="auth-badge">Admin Access</span>
            <span>Jeff & May</span>
            <button class="logout-btn" onclick="adminAuth.logout()">Logout</button>
        </div>
    </div>
    
    <div class="container">
        <!-- Winery Selector -->
        <div class="winery-selector">
            <div class="selector-header">
                <div class="selector-title">Winery Management Dashboard</div>
                <div class="winery-count" id="wineryCount">Loading...</div>
            </div>
            
            <!-- Currently Selected Winery -->
            <div class="current-selection" id="currentSelection" style="display: none;">
                <div class="current-winery">
                    <div class="current-name" id="currentName">No winery selected</div>
                    <div class="current-meta" id="currentMeta">Select a winery to manage</div>
                </div>
                <button class="change-btn" onclick="toggleWinerySelector()">Change Winery</button>
            </div>
            
            <!-- Winery Selection Grid -->
            <div class="winery-grid" id="wineryGrid">
                <div class="loading" style="grid-column: 1 / -1; text-align: center; padding: 2rem;"></div>
            </div>
        </div>
        
        <!-- Management Panels -->
        <div class="admin-panels" id="adminPanels" style="display: none;">
            <!-- Wine Management -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon wines-icon">🍷</div>
                    <div>
                        <div class="panel-title">Wine Management</div>
                        <div class="panel-subtitle" id="wineSubtitle">Manage wines for selected winery</div>
                    </div>
                </div>
                <div class="quick-stats">
                    <div class="stat">
                        <div class="stat-number" id="wineCount">-</div>
                        <div class="stat-label">Active Wines</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="categoryCount">-</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>
                <div class="panel-actions">
                    <a href="#" class="btn btn-primary" id="manageWinesBtn">Manage Wines</a>
                    <a href="#" class="btn btn-secondary" id="addWineBtn">Add Wine</a>
                </div>
            </div>
            
            <!-- Plateau Management -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon plateaux-icon">🧀</div>
                    <div>
                        <div class="panel-title">Food Board</div>
                        <div class="panel-subtitle" id="plateauSubtitle">Manage plateaux for selected winery</div>
                    </div>
                </div>
                <div class="quick-stats">
                    <div class="stat">
                        <div class="stat-number" id="plateauCount">-</div>
                        <div class="stat-label">Plateaux</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="plateauRange">0-8</div>
                        <div class="stat-label">Quantity Range</div>
                    </div>
                </div>
                <div class="panel-actions">
                    <a href="#" class="btn btn-primary" id="managePlateauxBtn">Manage Plateaux</a>
                    <a href="#" class="btn btn-secondary" id="addPlateauBtn">Add Plateau</a>
                </div>
            </div>
            
            <!-- Winery Settings -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon settings-icon">⚙️</div>
                    <div>
                        <div class="panel-title">Winery Settings</div>
                        <div class="panel-subtitle" id="settingsSubtitle">Configuration for selected winery</div>
                    </div>
                </div>
                <div class="quick-stats">
                    <div class="stat">
                        <div class="stat-number" id="maxGuests">-</div>
                        <div class="stat-label">Max Guests</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="winesPerGuest">-</div>
                        <div class="stat-label">Wines/Guest</div>
                    </div>
                </div>
                <div class="panel-actions">
                    <a href="#" class="btn btn-primary" id="editSettingsBtn">Edit Settings</a>
                    <a href="#" class="btn btn-secondary" id="viewAnalyticsBtn">Analytics</a>
                </div>
            </div>

            <!-- Session Management -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon sessions-icon">📋</div>
                    <div>
                        <div class="panel-title">Session Management</div>
                        <div class="panel-subtitle" id="sessionSubtitle">Manage active sessions for selected winery</div>
                    </div>
                </div>
                <div class="quick-stats">
                    <div class="stat">
                        <div class="stat-number" id="sessionCount">-</div>
                        <div class="stat-label">Active Sessions</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="todayCount">-</div>
                        <div class="stat-label">Today's Sessions</div>
                    </div>
                </div>
                <div class="panel-actions">
                    <a href="#" class="btn btn-primary" id="manageSessionsBtn">Manage Sessions</a>
                    <a href="#" class="btn btn-secondary" id="cleanupSessionsBtn">Cleanup</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Admin Dashboard Logic
        class AdminDashboard {
            constructor() {
                this.currentWinery = null;
                this.wineries = [];
                this.init();
            }
            
            async init() {
                // Check authentication
                if (!this.isAuthenticated()) {
                    window.location.href = '/admin/auth.html';
                    return;
                }
                
                // Load wineries
                await this.loadWineries();
                
                // Auto-select first winery if available
                if (this.wineries.length > 0) {
                    this.selectWinery(this.wineries[0]);
                }
            }
            
            isAuthenticated() {
                const token = localStorage.getItem('adminAuth');
                const authTime = localStorage.getItem('adminAuthTime');
                
                if (!token || !authTime) {
                    return false;
                }
                
                // Check if token is expired (1 hour)
                const oneHour = 60 * 60 * 1000;
                const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
                
                return !isExpired;
            }
            
          async loadWineries() {
                try {
                    const response = await fetch('/api/admin/settings', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        // FILTER: Only show active wineries in dashboard
                        this.wineries = (data.wineries || []).filter(winery => winery.is_active);
                        this.renderWineries();
                    } else {
                        throw new Error(`API Error: ${response.status}`);
                    }
                } catch (error) {
                    console.error('Error loading wineries:', error);
                    this.wineries = [];
                    this.renderWineries();
                }
            }
            
            renderWineries() {
                const grid = document.getElementById('wineryGrid');
                const count = document.getElementById('wineryCount');
                
                count.textContent = `${this.wineries.length} Wineries`;
                
                grid.innerHTML = this.wineries.map(winery => `
                    <div class="winery-card" onclick="adminDashboard.selectWinery(${JSON.stringify(winery).replace(/"/g, '&quot;')})">
                        <div class="winery-name">${winery.name}</div>
                        <div class="winery-meta">${winery.subscription_tier} • Code: ${winery.winery_code}</div>
                        <div class="winery-stats">
                            <span>${winery.wine_count || 0} wines</span>
                            <span>${winery.plateau_count || 0} plateaux</span>
                        </div>
                    </div>
                `).join('');
            }
            
            selectWinery(winery) {
                this.currentWinery = winery;
                this.updateCurrentSelection();
                this.updatePanels();
                this.showPanels();
                
                // Update active card
                document.querySelectorAll('.winery-card').forEach(card => {
                    card.classList.remove('active');
                });
                event?.target?.closest('.winery-card')?.classList.add('active');
            }
            
            updateCurrentSelection() {
                const selection = document.getElementById('currentSelection');
                const name = document.getElementById('currentName');
                const meta = document.getElementById('currentMeta');
                
                if (this.currentWinery) {
                    name.textContent = this.currentWinery.name;
                    meta.textContent = `${this.currentWinery.subscription_tier} • Code: ${this.currentWinery.winery_code} • ${this.currentWinery.wine_count || 0} wines • ${this.currentWinery.plateau_count || 0} plateaux`;
                    selection.style.display = 'flex';
                } else {
                    selection.style.display = 'none';
                }
            }
            
            updatePanels() {
                if (!this.currentWinery) return;
                
                const winery = this.currentWinery;
                const wineryParam = `?winery=${winery.id}`;
                // Update subtitles
                document.getElementById('wineSubtitle').textContent = `Manage wines for ${winery.name}`;
                document.getElementById('plateauSubtitle').textContent = `Manage plateaux for ${winery.name}`;
                document.getElementById('settingsSubtitle').textContent = `Configuration for ${winery.name}`;
                
                // Update stats
                document.getElementById('wineCount').textContent = winery.wine_count || 0;
                document.getElementById('categoryCount').textContent = winery.category_count || '0';
                document.getElementById('plateauCount').textContent = winery.plateau_count || 0;
                document.getElementById('maxGuests').textContent = winery.max_guests_per_group || 6;
                document.getElementById('winesPerGuest').textContent = winery.wines_per_guest || 3;
                
                // Update session management
                document.getElementById('sessionSubtitle').textContent = `Manage sessions for ${winery.name}`;
                document.getElementById('sessionCount').textContent = winery.session_count || 0;
                document.getElementById('todayCount').textContent = winery.today_sessions || 0;

                // Update session action links
                document.getElementById('manageSessionsBtn').href = `/admin/sessions.html${wineryParam}`;
                document.getElementById('cleanupSessionsBtn').onclick = (e) => {
                    e.preventDefault();
                    this.showSessionCleanup(winery.id);
                };

                // Update action links
                
                document.getElementById('manageWinesBtn').href = `/admin/wines.html${wineryParam}`;
                document.getElementById('addWineBtn').href = `/admin/wines.html${wineryParam}&action=add`;
                document.getElementById('managePlateauxBtn').href = `/admin/plateaux.html${wineryParam}`;
                document.getElementById('addPlateauBtn').href = `/admin/plateaux.html${wineryParam}&action=add`;
                document.getElementById('editSettingsBtn').href = `/admin/settings.html${wineryParam}`;
                document.getElementById('editSettingsBtn').onclick = () => {
                    // Open settings page and auto-edit this winery
                    localStorage.setItem('editWineryId', winery.id);
                    window.location.href = '/admin/settings.html';
                };
            }
            
            showPanels() {
                document.getElementById('adminPanels').style.display = 'grid';
            }

            async showSessionCleanup(wineryId) {
                const sessionId = prompt('Enter session ID to delete:');
                if (!sessionId) return;
                
                if (!confirm(`Delete session ${sessionId}? This cannot be undone.`)) return;
                
                try {
                    const response = await fetch(`/api/admin/cleanup-session?sessionId=${sessionId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        alert(result.message);
                        this.loadWineries(); // Refresh counts
                    } else {
                        alert(`Error: ${result.error}`);
                    }
                } catch (error) {
                    alert('Failed to delete session');
                }
            }

            logout() {
                localStorage.removeItem('adminAuth');
                localStorage.removeItem('adminAuthTime');
                window.location.href = '/admin/auth.html';
            }
        }
        
        // Global auth object for logout
        const adminAuth = {
            logout: () => {
                localStorage.removeItem('adminAuth');
                localStorage.removeItem('adminAuthTime');
                window.location.href = '/admin/auth.html';
            }
        };
        
        // Global function for winery selector toggle
        function toggleWinerySelector() {
            const grid = document.getElementById('wineryGrid');
            grid.style.display = grid.style.display === 'none' ? 'grid' : 'none';
        }
        
        // Initialize dashboard
        const adminDashboard = new AdminDashboard();
    </script>
</body>
</html>