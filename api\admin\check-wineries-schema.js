import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check current wineries table structure
    const columns = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'wineries' 
      ORDER BY ordinal_position;
    `);

    // Check if operational_hours and timezone columns exist
    const hasOperationalHours = columns.rows.some(col => col.column_name === 'operational_hours');
    const hasTimezone = columns.rows.some(col => col.column_name === 'timezone');

    res.status(200).json({ 
      success: true,
      columns: columns.rows,
      hasOperationalHours,
      hasTimezone,
      message: 'Wineries table schema checked'
    });

  } catch (error) {
    console.error('Schema check error:', error);
    res.status(500).json({ 
      error: 'Failed to check schema: ' + error.message 
    });
  }
}