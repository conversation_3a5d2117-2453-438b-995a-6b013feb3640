// api/admin/migrate-guest-limits.js
// One-time migration to allow 1-8 guests per group instead of 2-8

import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {

    console.log('Starting guest limits migration...');

    // Drop and recreate constraints to allow 1-8 guests
    await query(`
      ALTER TABLE wineries 
      DROP CONSTRAINT IF EXISTS wineries_max_guests_per_group_check,
      DROP CONSTRAINT IF EXISTS wineries_min_guests_per_group_check,
      DROP CONSTRAINT IF EXISTS valid_guest_limits;
    `);

    console.log('Dropped old constraints...');

    await query(`
      ALTER TABLE wineries 
      ADD CONSTRAINT wineries_max_guests_per_group_check 
      CHECK (max_guests_per_group BETWEEN 1 AND 8),
      ADD CONSTRAINT wineries_min_guests_per_group_check 
      CHECK (min_guests_per_group BETWEEN 1 AND 8),
      ADD CONSTRAINT valid_guest_limits 
      CHECK (min_guests_per_group <= max_guests_per_group);
    `);

    console.log('Added new constraints allowing 1-8 guests...');

    // Update ALL existing wineries to allow single guests
    const updateResult = await query(`
      UPDATE wineries 
      SET min_guests_per_group = 1;
    `);

    console.log(`Updated ${updateResult.rowCount} existing winery records`);

    // Change the DEFAULT value for future wineries
    await query(`
      ALTER TABLE wineries 
      ALTER COLUMN min_guests_per_group SET DEFAULT 1;
    `);

    console.log('Updated default value for new wineries');

    console.log(`Updated ${updateResult.rowCount} winery records`);

    // Verify the changes
    const verification = await query(`
      SELECT id, name, slug, min_guests_per_group, max_guests_per_group 
      FROM wineries 
      ORDER BY id;
    `);

    res.status(200).json({ 
      success: true, 
      message: 'Guest limits successfully updated - single guests now allowed',
      wineries: verification.rows,
      updated_records: updateResult.rowCount
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({ 
      error: 'Migration failed: ' + error.message,
      details: error.stack
    });
  }
}