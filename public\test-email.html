<!DOCTYPE html>
<html>
<head>
    <title>Email Test</title>
</head>
<body>
    <h1>Email Function Test</h1>
    <button onclick="testEmail()">Send Test Email</button>
    <button onclick="checkWinery()">Check Winery Data</button>
    <div id="result"></div>
    
    <script>
        async function checkWinery() {
            try {
                const response = await fetch('/api/guest/menu');
                const data = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <h3>Winery Info:</h3>
                    <p>Name: ${data.winery.name}</p>
                    <p>Tier: ${data.winery.subscription_tier || 'NOT FOUND'}</p>
                    <p>Logo: ${data.winery.logo || 'NO LOGO'}</p>
                `;
            } catch (error) {
                document.getElementById('result').textContent = 'Error: ' + error.message;
            }
        }
        
        async function testEmail() {
            const email = prompt('Enter your email:');
            if (!email) return;
            
            try {
                const response = await fetch('/api/email/session-recap', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        groupCode: '263D15AF',
                        guestEmail: email,
                        language: 'fr'
                    })
                });
                
                const data = await response.json();
                alert(JSON.stringify(data, null, 2));
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>