<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EZvino.app - Invités / Guests</title>
    <link rel="stylesheet" href="../shared/theme.css">
    <script src="../shared/premiumFeatures.js"></script>
    <script>
    console.log('PremiumFeatures loaded:', typeof PremiumFeatures);
    </script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        
       
       
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header with Winery Branding -->
        <header class="header">
            <div class="header-content">
                <!-- Row 1: Language Switcher only (right side) -->
                <div class="header-row-1">
                    <div style="flex: 1;"></div> <!-- Empty left side -->
                </div>
                
                <!-- Row 2: Logo + Winery Name (left-aligned) -->
                <div class="header-row-2">
                    <div class="winery-logo" id="wineryLogo">CT</div>
                    <h1 class="winery-name" id="wineryName">Winery Loading</h1>
                </div>
                
                <!-- Row 3: Centered Subtitle -->
                <div class="header-row-3">
                    <p class="subtitle">
                        <span class="french-text-white">Sélectionnez votre parfaite expérience</span>
                        <span class="english-text-white">Select your perfect experience</span>
                    </p>
                </div>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loadingState" class="loading">
            <div class="loading-spinner"></div>
            <p>
                <span class="french-text">Chargement du menu...</span>
                <span class="english-text">Loading menu...</span>
            </p>
        </div>

        <!-- Success Message -->
        <div id="successMessage" class="success-message">
                <span class="french-text">Sélection confirmée!</span>
                <span class="english-text">Selection confirmed!</span>
            <p>
                <span class="french-text">SVP rejoignez-nous au comptoir de la boutique pour payer et débuter votre aventure gustative!</span>
                <span class="english-text">Please join us at the store counter to pay and begin your tasting adventure!</span>
            </p>
        </div>

        <!-- Main Content -->
        <main class="main-content" id="mainContent" style="display: none;">
            
            <!-- Group Name Section -->
            <section class="section">
                <div class="section-header">
                    <span class="french-text">Nom du groupe (requis)</span>                    
                    <span class="english-text">Group name (required)</span>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <label class="form-label">
                            <span class="french-text">Entrez un nom pour votre groupe</span>
                            <span class="english-text">Enter a name for your group</span>
                        </label>
                        <input type="text" id="groupNameFr" class="form-input french-text" placeholder="Ex: Famille Dupont" required>
                        <input type="text" id="groupNameEn" class="form-input english-text" placeholder="Ex: Smith Family" required>
                    </div>
                </div>
            </section>

            <!-- Dynamic Guest Management -->
            <section class="section">
                <div class="section-header">
                    <span class="french-text">Prénom des invités (requis)</span>
                    <span class="english-text">Guests first names (required)</span>
                    <span id="guestCounter" style="float: right; font-size: 14px;">2 Invités</span>
                </div>
                <div class="section-content">
                    <div id="guestList" class="guest-list">
                        <!-- Dynamic guest inputs will be added here -->
                    </div>
                    <button type="button" id="addGuestBtn" class="add-guest-btn">
                        <span>+</span>
                        <span class="french-text">Ajoutez un invité</span>
                        <span class="english-text">Add a guest</span>
                    </button>
                </div>
            </section>

            <!-- Plateau Selection Section -->
            <section class="section">
                <div class="section-header">
                    <span class="french-text">À grignoter (optionnel)</span>
                    <span class="english-text">Snack (optional)</span>
                    <p class="subtitle">
                            <span class="french-text">25$ par plateau +tx</span> 
                            <span class="english-text">25$ per board +tx</span>
                    </p>
                </div>
                <div class="section-content">
                    <div id="plateauGrid">
                        <!-- Plateaux will be loaded dynamically -->
                    </div>
                </div>
            </section>

            <!-- Wine Selection Section -->
            <section class="section">
                <div class="section-header">
                    <span class="french-text">Sélection de vins et cidres</span>
                    <span class="english-text">Selection of wines and ciders</span>
                <p class="subtitle">
                            <span class="french-text">4 choix/12$ par personne tx in.</span>
                            <span class="english-text">4 choices/12$ per person tx in.</span>
                </p>
                </div>
                <div class="section-content">
                    <div id="validationMessages"></div>
                    <div id="wineGrid" class="wine-grid">
                        <!-- Wines will be loaded dynamically -->
                    </div>
                </div>
            </section>

            <!-- Order Summary -->
            <div class="order-summary" id="orderSummary" style="display: none;">
                    <span class="french-text">Résumé de la sélection</span>
                    <span class="english-text">Selection summary</span>
                <div id="summaryContent">
                    <!-- Summary will be generated dynamically -->
                </div>
            </div>
        </main>

        <!-- Submit Section -->
        <div class="submit-section" id="submitSection" style="display: none;">
            <button class="submit-btn" id="submitOrder" disabled>
                <span class="french-text">Soumettez votre sélection</span>
                <span class="english-text">Submit your selection</span>
            </button>
        </div>
    </div>

    <script>
        // =============================================
        // Language switching - Complete Version
        // =============================================

        function createLanguageSwitcher() {
            const switchButton = document.createElement('div');
            switchButton.className = 'language-switcher';
            switchButton.innerHTML = `
                <button class="lang-btn" id="fr-btn">FR</button>
                <button class="lang-btn" id="en-btn">EN</button>
            `;
            
            // Append to header instead of body
            const headerRow1 = document.querySelector('.header-row-1');
            if (headerRow1) {
                headerRow1.appendChild(switchButton);
            } else {
                document.body.appendChild(switchButton);
            }
            
            // Add click handlers with localStorage
            document.getElementById('fr-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'fr');
                document.body.setAttribute('data-lang', 'fr');
                document.getElementById('fr-btn').classList.add('active');
                document.getElementById('en-btn').classList.remove('active');
                console.log('French mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };

            document.getElementById('en-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'en');
                document.body.setAttribute('data-lang', 'en');
                document.getElementById('en-btn').classList.add('active');
                document.getElementById('fr-btn').classList.remove('active');
                console.log('English mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };
            
            // Load saved preference or default to French
            const savedLang = localStorage.getItem('selectedLanguage') || 'fr';
            document.getElementById(savedLang + '-btn').click();
        }

        // Run when page loads
        document.addEventListener('DOMContentLoaded', createLanguageSwitcher);

        // =============================================
        // WINE TASTING GUEST APP - V7 DYNAMIC GUESTS
        // =============================================

        class WineTastingApp {
            constructor() {
                this.winery = null;
                this.wines = [];
                this.plateaux = [];
                this.guests = [
                    { id: 1, name: '' }
                ];
                this.wineSelections = {}; // { wineId: [guestId1, guestId2, ...] }
                this.plateauQuantities = {};
                this.API_BASE = '/api';
                
                this.init();
            }

            async init() {
                this.setupEventListeners();
                await this.loadWineryData();
                this.renderGuests();
            }

            setupEventListeners() {
                // Add guest button
                document.getElementById('addGuestBtn').addEventListener('click', () => {
                    this.addGuest();
                });

                // Submit order
                document.getElementById('submitOrder').addEventListener('click', () => {
                    this.submitOrder();
                });

                // Group name input - handle both language inputs
                document.getElementById('groupNameFr').addEventListener('input', (e) => {
                    document.getElementById('groupNameEn').value = e.target.value;
                    this.updateOrderValidation();
                });

                document.getElementById('groupNameEn').addEventListener('input', (e) => {
                    document.getElementById('groupNameFr').value = e.target.value;
                    this.updateOrderValidation();
                });
            }

            async loadWineryData() {
                try {
                    console.log('Loading winery data...');
                    const response = await fetch(`${this.API_BASE}/guest/menu`, {
                        headers: {
                            'X-Winery-Slug': 'loading winery...'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log('Winery data loaded:', data);
                    
                    this.wines = data.wines;      
                    this.plateaux = data.plateaux; 
                    this.winery = data.winery;    

                    if (data.isOpen === false) {
                    this.showClosedOverlay(data.winery.closedMessage);
                    return; // Stop loading the rest
                    }

                    // Initialize wine selections
                    this.wines.forEach(wine => {
                        this.wineSelections[wine.id] = [];
                    });

                    this.renderWineryInfo();
                    this.renderPlateaux();
                    this.renderWines();
                    this.showMainContent();

                } catch (error) {
                    console.error('Error loading winery data:', error);
                    this.showErrorMessage(error.message);
                }
            }

            renderWineryInfo() {
                document.getElementById('wineryName').textContent = this.winery.name;
                
                // Show logo only for premium wineries, otherwise show nothing
                const logoElement = document.getElementById('wineryLogo');

                // DEBUG LOGS
                console.log('Winery data:', this.winery);
                console.log('Subscription tier:', this.winery.subscription_tier);
                console.log('Logo URL:', this.winery.logo);
                console.log('Has logo feature:', PremiumFeatures.hasFeature(this.winery, 'logo_display'));

                if (PremiumFeatures.hasFeature(this.winery, 'logo_display') && this.winery.logo) {
                    logoElement.innerHTML = `<img src="${this.winery.logo}" alt="${this.winery.name}">`;
                    logoElement.style.display = 'block';
                    document.querySelector('.winery-name').style.marginTop = '0px';
                } else {
                    logoElement.style.display = 'none';
                    document.querySelector('.winery-name').style.marginTop = '20px';
                }
            }

            getWineryInitials(name) {
                return name.split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
            }

            renderGuests() {
                const guestList = document.getElementById('guestList');
                guestList.innerHTML = '';

                this.guests.forEach((guest, index) => {
                    const guestItem = this.createGuestItem(guest, index);
                    guestList.appendChild(guestItem);
                });

                this.updateGuestCounter();
                this.updateAddGuestButton();
                this.renderWines(); // Re-render wines when guests change
                this.updateOrderValidation();
            }

            createGuestItem(guest, index) {
                const item = document.createElement('div');
                item.className = 'guest-item';
                
                const minGuests = this.winery?.min_guests_per_group || 1;
                const canDelete = this.guests.length > minGuests;
                
                item.innerHTML = `
                    <div class="guest-number">${guest.id}</div>
                    <input type="text" class="guest-name-input french-text" placeholder="Entrez votre prénom ${guest.id}" value="${guest.name}">
                    <input type="text" class="guest-name-input english-text" placeholder="Enter your first name ${guest.id}" value="${guest.name}">
                    ${canDelete ? '<button type="button" class="remove-guest-btn">×</button>' : ''}
                `;

                const nameInputs = item.querySelectorAll('.guest-name-input');
                nameInputs.forEach(input => {
                    input.addEventListener('input', (e) => {
                        const oldName = this.guests[index].name;
                        this.guests[index].name = e.target.value;
                        
                        // Sync the other input with the same value
                        nameInputs.forEach(otherInput => {
                            if (otherInput !== e.target) {
                                otherInput.value = e.target.value;
                            }
                        });
                        
                        // If name was cleared, remove all wine selections for this guest
                        if (oldName.trim() && !e.target.value.trim()) {
                            Object.keys(this.wineSelections).forEach(wineId => {
                                this.wineSelections[wineId] = this.wineSelections[wineId].filter(id => id !== this.guests[index].id);
                            });
                        }
                        
                        this.renderWines(); // Update wine section with new name
                        this.updateOrderValidation();
                    });
                });

                const removeBtn = item.querySelector('.remove-guest-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', () => {
                        this.removeGuest(guest.id);
                    });
                }

                return item;
            }

            addGuest() {
                if (this.guests.length < 8) {
                    const newId = Math.max(...this.guests.map(g => g.id)) + 1;
                    this.guests.push({ id: newId, name: '' });
                    this.renderGuests();
                }
            }
            
            updateGuestCounter() {
                const counter = document.getElementById('guestCounter');
                if (!counter) return; // Exit if element doesn't exist
                
                const count = this.guests.length;
                // Check current language mode
                const currentLang = document.body.getAttribute('data-lang') || 'both';
                
                if (currentLang === 'fr') {
                    counter.textContent = `${count} ${count === 1 ? 'invité' : 'invités'}`;
                } else if (currentLang === 'en') {
                    counter.textContent = `${count} ${count === 1 ? 'guest' : 'guests'}`;
                } else {
                    counter.textContent = `${count} ${count === 1 ? 'invité' : 'invités'} / ${count === 1 ? 'guest' : 'guests'}`;
                }
            }

            updateAddGuestButton() {
                const btn = document.getElementById('addGuestBtn');
                const maxGuests = this.winery?.max_guests_per_group || 8;
                btn.disabled = this.guests.length >= maxGuests;
            }

            renderPlateaux() {
                const plateauGrid = document.getElementById('plateauGrid');
                plateauGrid.innerHTML = '';

                this.plateaux.forEach(plateau => {
                    const plateauCard = this.createPlateauCard(plateau);
                    plateauGrid.appendChild(plateauCard);
                });
            }

            createPlateauCard(plateau) {
                const card = document.createElement('div');
                card.className = 'plateau-card';
                
                this.plateauQuantities[plateau.id] = 0;

                card.innerHTML = `
                    <div class="plateau-header">
                        <div class="plateau-info">
                            <div class="wine-name">
                                <span class="french-text">${plateau.name_fr}</span> 
                                <span class="english-text">${plateau.name_en}</span>
                            </div>
                            <div class="wine-description">
                                <div class="french-text">${plateau.description_fr || ''}</div>
                                <div class="english-text">${plateau.description_en || ''}</div>
                            </div>
                        </div>
                    </div>
                    <div class="plateau-controls">
                        <div class="quantity-control">
                            <button type="button" class="quantity-btn" data-action="decrease" data-plateau-id="${plateau.id}">−</button>
                            <span class="quantity-display" data-plateau-id="${plateau.id}">0</span>
                            <button type="button" class="quantity-btn" data-action="increase" data-plateau-id="${plateau.id}">+</button>
                        </div>
                    </div>
                `;

                card.querySelectorAll('.quantity-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const action = e.target.dataset.action;
                        const plateauId = parseInt(e.target.dataset.plateauId);
                        this.updatePlateauQuantity(plateauId, action);
                    });
                });

                return card;
            }

            updatePlateauQuantity(plateauId, action) {
                const maxPlateaux = this.winery?.max_plateaux_per_group || 2;
                const currentTotal = Object.values(this.plateauQuantities).reduce((sum, qty) => sum + qty, 0);
                
                if (action === 'increase') {
                    if (currentTotal < maxPlateaux) {
                        this.plateauQuantities[plateauId]++;
                    } else {
                        alert('Limite de plateaux atteinte / Board limit reached');
                        return;
                    }
                } else if (action === 'decrease') {
                    if (this.plateauQuantities[plateauId] > 0) {
                        this.plateauQuantities[plateauId]--;
                    }
                }

                const display = document.querySelector(`[data-plateau-id="${plateauId}"].quantity-display`);
                display.textContent = this.plateauQuantities[plateauId];

                this.updateOrderSummary();
                this.updateOrderValidation();
            }

            renderWines() {
                const wineGrid = document.getElementById('wineGrid');
                wineGrid.innerHTML = ''; // Complete DOM clear

                this.wines.forEach(wine => {
                    const wineCard = this.createWineCard(wine);
                    wineGrid.appendChild(wineCard);
                });
                
                // Force refresh of all wine selection states after DOM rebuild
                this.updateAllGuestCounters();
            }

            removeGuest(guestId) {
                const minGuests = this.winery?.min_guests_per_group || 1;
                
                if (this.guests.length > minGuests) {
                    // SURGICAL FIX: Remove specific guest checkboxes from DOM first
                    document.querySelectorAll(`[id*="guest-${guestId}"]`).forEach(element => {
                        element.closest('.guest-checkbox')?.remove();
                    });
                    
                    // Remove guest from array
                    this.guests = this.guests.filter(g => g.id !== guestId);
                    
                    // Clean up ALL wine selections for this guest
                    Object.keys(this.wineSelections).forEach(wineId => {
                        this.wineSelections[wineId] = this.wineSelections[wineId].filter(id => id !== guestId);
                    });
                    
                    // Re-render guests section only (preserve wine selections)
                    this.renderGuests();
                    this.updateOrderValidation();
                }
            }

            createWineCard(wine) {
                const card = document.createElement('div');
                card.className = 'wine-card';
                card.dataset.wineId = wine.id;
                                
                card.innerHTML = `
                    <div class="wine-header">
                        <div class="wine-image">
                         </div>
                        <div class="wine-info">
                            <div class="wine-name">${wine.name}</div>
                            <div class="wine-category">
                                <span class="french-text">${wine.category_name_fr}</span>
                                <span class="english-text">${wine.category_name_en}</span>
                            </div>
                        </div>
                    </div>
                    <div class="wine-description">
                        <div class="french-text">${wine.description_fr || 'Description disponible bientôt'}</div>
                        <div class="english-text">${wine.description_en || 'Description coming soon'}</div>
                    </div>
                    <div class="guest-wine-selection">
                        <div class="guest-selection-header">
                            <span class="french-text">Cochez pour sélectionner</span>
                            <span class="english-text">Check to select</span>
                        </div>
                        <div class="guest-checkboxes" id="guest-checkboxes-${wine.id}">
                            <!-- Guest checkboxes will be added dynamically -->
                        </div>
                    </div>
                `;

                // Add guest checkboxes - only for guests with names
                const checkboxContainer = card.querySelector(`#guest-checkboxes-${wine.id}`);
                checkboxContainer.innerHTML = ''; // Force clear existing checkboxes
                this.guests.forEach(guest => {
                    if (guest.name.trim()) { // Only show checkboxes for named guests
                        const guestCheckbox = this.createGuestCheckbox(wine.id, guest);
                        checkboxContainer.appendChild(guestCheckbox);
                    }
                });

                return card;
            }

            createGuestCheckbox(wineId, guest) {
                const checkbox = document.createElement('div');
                checkbox.className = 'guest-checkbox';
                
                const isSelected = this.wineSelections[wineId].includes(guest.id);
                const guestSelectionCount = this.getGuestWineCount(guest.id);
                const maxWines = this.winery?.wines_per_guest || 4;
                
                if (isSelected) {
                    checkbox.classList.add('selected');
                }

                // Language-aware display name
                const currentLang = document.body.getAttribute('data-lang') || 'fr';
                const guestDisplayName = guest.name.trim() ? guest.name : 
                    (currentLang === 'en' ? `Guest ${guest.id}` : `Invité ${guest.id}`);
                
                checkbox.innerHTML = `
                    <input type="checkbox" id="wine-${wineId}-guest-${guest.id}" ${isSelected ? 'checked' : ''}>
                    <label for="wine-${wineId}-guest-${guest.id}">${guestDisplayName}</label>
                    <span class="wine-selection-counter">${guestSelectionCount}/${maxWines}</span>
                `;

                const input = checkbox.querySelector('input');
                input.addEventListener('change', (e) => {
                    this.toggleWineSelection(wineId, guest.id, e.target.checked);
                });

                return checkbox;
            }

            toggleWineSelection(wineId, guestId, isSelected) {
                const maxWines = this.winery?.wines_per_guest || 4;
                const currentCount = this.getGuestWineCount(guestId);
                
                // Find the guest
                const guest = this.guests.find(g => g.id === guestId);
                
                if (isSelected) {
                    // Prevent selection for guests without names
                    if (!guest || !guest.name.trim()) {
                        const currentLang = document.body.getAttribute('data-lang') || 'fr';
                        const message = currentLang === 'en' 
                            ? `Please enter a name for Guest ${guestId} first.`
                            : `Veuillez d'abord entrer un nom pour l'Invité ${guestId}.`;
                        alert(message);
                        // Uncheck the checkbox
                        document.getElementById(`wine-${wineId}-guest-${guestId}`).checked = false;
                        return;
                    }
                    
                    // Check if guest can select more wines
                    if (currentCount >= maxWines) {
                        const currentLang = document.body.getAttribute('data-lang') || 'fr';
                        const message = currentLang === 'en'
                            ? `Limit reached! Each guest can select ${maxWines} wines maximum.`
                            : `Limite atteinte! Chaque invité peut sélectionner ${maxWines} vins maximum.`;
                        alert(message);
                        // Uncheck the checkbox
                        document.getElementById(`wine-${wineId}-guest-${guestId}`).checked = false;
                        return;
                    }
                    
                    // Add selection
                    if (!this.wineSelections[wineId].includes(guestId)) {
                        this.wineSelections[wineId].push(guestId);
                    }
                } else {
                    // Remove selection
                    this.wineSelections[wineId] = this.wineSelections[wineId].filter(id => id !== guestId);
                }

                // Update UI
                this.updateWineCheckboxDisplay(wineId, guestId);
                this.updateAllGuestCounters();
                this.updateOrderSummary();
                this.updateOrderValidation();
            }

            updateWineCheckboxDisplay(wineId, guestId) {
                const checkbox = document.querySelector(`#wine-${wineId}-guest-${guestId}`).closest('.guest-checkbox');
                const isSelected = this.wineSelections[wineId].includes(guestId);
                
                checkbox.classList.toggle('selected', isSelected);
            }

            updateAllGuestCounters() {
                this.guests.forEach(guest => {
                    const count = this.getGuestWineCount(guest.id);
                    const maxWines = this.winery?.wines_per_guest || 4;
                    
                    document.querySelectorAll(`[for*="guest-${guest.id}"]`).forEach(label => {
                        const counter = label.parentElement.querySelector('.wine-selection-counter');
                        if (counter) {
                            counter.textContent = `${count}/${maxWines}`;
                        }
                    });
                });
            }

            getGuestWineCount(guestId) {
                return Object.values(this.wineSelections).reduce((count, selections) => {
                    return count + (selections.includes(guestId) ? 1 : 0);
                }, 0);
            }
            
            updateValidationMessages() {
                const validationContainer = document.getElementById('validationMessages');
                const maxWines = this.winery?.wines_per_guest || 4;
                const messages = [];

                this.guests.forEach(guest => {
                    const count = this.getGuestWineCount(guest.id);
                    const guestName = guest.name || `<span class="french-text">Invité ${guest.id}</span><span class="english-text">Guest ${guest.id}</span>`;
                    
                    if (count < maxWines) {
                        messages.push(`${guestName}: ${count}/${maxWines} <span class="french-text">vins sélectionnés</span><span class="english-text">wines selected</span>`);
                    }
                });
               
            }

            updateOrderSummary() {
                const summaryContent = document.getElementById('summaryContent');
                const orderSummary = document.getElementById('orderSummary');
            
                if (this.guests.length === 0) {
                    orderSummary.style.display = 'none';
                    return;
                }
                
                const groupName = (document.getElementById('groupNameFr').value || document.getElementById('groupNameEn').value).trim() || 'Groupe sans nom / Unnamed group';
                
                // Get plateau count and info
                let totalPlateaux = 0;
                let plateauInfo = '';
                Object.entries(this.plateauQuantities).forEach(([plateauId, quantity]) => {
                    if (quantity > 0) {
                        totalPlateaux += quantity;
                        const plateau = this.plateaux.find(p => p.id == plateauId);
                        if (plateauInfo) plateauInfo += ', ';
                        const currentLang = document.body.getAttribute('data-lang') || 'fr';
                        const plateauName = currentLang === 'en' ? plateau.name_en : plateau.name_fr;
                        plateauInfo += plateauName;
                    }
                });

                // Count total wines selected
                let totalWines = 0;
                this.guests.forEach(guest => {
                    if (guest.selectedWines) {
                        totalWines += guest.selectedWines.length;
                    }
                });

                // Build complete summary
                let summaryHTML = `
                    <div class="summary-tile">
                        <h4>
                            <span class="french-text">${groupName}</span>
                            <span class="english-text">${groupName}</span>
                        </h4>
                        <p>
                            <span class="french-text"><strong>${this.guests.length} ${this.guests.length === 1 ? 'invité' : 'invités'}</strong></span>
                            <span class="english-text"><strong>${this.guests.length} ${this.guests.length === 1 ? 'guest' : 'guests'}</strong></span>
                        </p>
                        ${totalPlateaux > 0 ? `
                        <p>
                            <span class="french-text">
                                ${totalPlateaux} ${totalPlateaux === 1 ? "plateau" : "plateaux"}: ${plateauInfo}
                            </span>
                            <span class="english-text">
                                ${totalPlateaux} ${totalPlateaux === 1 ? "board" : "boards"}: ${plateauInfo}
                            </span>
                        </p>` : ''}
                    </div>
                `;

                summaryContent.innerHTML = summaryHTML;
                orderSummary.style.display = 'block';
            }

            updateOrderValidation() {
                const groupName = (document.getElementById('groupNameFr').value || document.getElementById('groupNameEn').value).trim();
                const maxWines = this.winery?.wines_per_guest || 4;
                
                // Check if all NAMED guests have selected the required number of wines
                const namedGuests = this.guests.filter(guest => guest.name.trim());
                const allGuestsValid = namedGuests.length > 0 && namedGuests.every(guest => {
                    return this.getGuestWineCount(guest.id) === maxWines;
                });

                const namedGuestCount = this.guests.filter(guest => guest.name.trim()).length;
                const minGuests = this.winery?.min_guests_per_group || 1;

                const isValid = groupName && 
                            namedGuestCount >= minGuests && 
                            allGuestsValid;

                const submitBtn = document.getElementById('submitOrder');
                const submitSection = document.getElementById('submitSection');
                
                submitBtn.disabled = !isValid;
                submitSection.style.display = this.guests.length > 0 ? 'block' : 'none';

                // Update button text based on validation
                if (isValid) {
                    submitBtn.innerHTML = `
                        <span class="french-text">Soumettez votre sélection</span>
                        <span class="english-text">Submit your selection</span>
                    `;
                } else if (!groupName) {
                    submitBtn.innerHTML = `
                        <span class="french-text">Entrez un nom de groupe</span>
                        <span class="english-text">Enter a group name</span>
                    `;
                } else if (namedGuestCount < minGuests) {
                    const frenchGuestWord = minGuests === 1 ? "invité" : "invités";
                    const englishGuestWord = minGuests === 1 ? "guest" : "guests";

                    submitBtn.innerHTML = `
                        <span class="french-text">Minimum ${minGuests} ${frenchGuestWord} requis</span>
                        <span class="english-text">Minimum ${minGuests} ${englishGuestWord} required</span>
                    `;
                } else if (!allGuestsValid) {
                    const frenchWineWord = maxWines === 1 ? "vin" : "vins";
                    const englishWineWord = maxWines === 1 ? "wine" : "wines";

                    submitBtn.innerHTML = `
                        <span class="french-text">Sélectionnez ${maxWines} ${frenchWineWord} par invité</span>
                        <span class="english-text">Select ${maxWines} ${englishWineWord} per guest</span>
                    `;
                }
            }

            updateGuestLabels() {
                const currentLang = document.body.getAttribute('data-lang') || 'fr';
                
                // Update all guest labels in wine checkboxes
                this.guests.forEach(guest => {
                    const labels = document.querySelectorAll(`label[for*="guest-${guest.id}"]`);
                    labels.forEach(label => {
                        const guestDisplayName = guest.name.trim() ? guest.name : 
                            (currentLang === 'en' ? `Guest ${guest.id}` : `Invité ${guest.id}`);
                        label.textContent = guestDisplayName;
                    });
                });
            }

            async submitOrder() {
                try {
                    const submitBtn = document.getElementById('submitOrder');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <span class="french-text"></span>
                        <span class="english-text"></span>
                    `;

                    // Prepare order data
                    const orderData = {
                        groupName: (document.getElementById('groupNameFr').value || document.getElementById('groupNameEn').value).trim(),
                        guests: this.guests
                        .filter(guest => guest.name.trim()) // Only include guests with names
                        .map(guest => ({
                            name: guest.name.trim(),
                            wines: Object.keys(this.wineSelections)
                                .filter(wineId => this.wineSelections[wineId].includes(guest.id))
                                .map(wineId => parseInt(wineId))
                        })),
                        plateauOrders: Object.entries(this.plateauQuantities)
                            .filter(([id, qty]) => qty > 0)
                            .map(([plateauId, count]) => ({plateauId: parseInt(plateauId), count}))
                    };

                    console.log('Submitting order:', orderData);

                    // Submit to API
                    const response = await fetch(`${this.API_BASE}/guest/session`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Winery-Slug': 'loading winery...'
                        },
                        body: JSON.stringify(orderData)
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to create session');
                    }

                    const result = await response.json();
                    console.log('Session API response:', result);
                    
                    // Redirect to session page immediately
                    window.location.href = `session.html?code=${result.session.groupCode}`;

                } catch (error) {
                    console.error('Error submitting order:', error);
                    alert(`Error: ${error.message}`);
                    
                    // Re-enable button
                    const submitBtn = document.getElementById('submitOrder');
                    submitBtn.disabled = false;
                    this.updateOrderValidation();
                }
            }

            generateGuestData() {
                return this.guests.map(guest => ({
                    guest_name: guest.name || `Invité ${guest.id} / Guest ${guest.id}`,
                    guest_number: guest.id
                }));
            }

            generateWineOrders() {
                const orders = [];
                
                Object.entries(this.wineSelections).forEach(([wineId, selectedGuests]) => {
                    selectedGuests.forEach(guestId => {
                        orders.push({
                            guest_number: guestId,
                            wine_id: parseInt(wineId)
                        });
                    });
                });
                
                return orders;
            }

            generatePlateauOrders() {
                const orders = [];
                
                Object.entries(this.plateauQuantities).forEach(([plateauId, quantity]) => {
                    if (quantity > 0) {
                        orders.push({
                            plateau_id: parseInt(plateauId),
                            plateau_count: quantity
                        });
                    }
                });
                
                return orders;
            }

            showMainContent() {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
            }

            showSuccessMessage() {
                const successMessage = document.getElementById('successMessage');
                successMessage.classList.add('show');
            }

            showErrorMessage(message) {
                const errorText = message || 'Erreur lors du chargement / Loading error. Veuillez réessayer / Please try again.';
                
                document.getElementById('loadingState').innerHTML = `
                    <div style="color: #dc2626; text-align: center; padding: 40px;">
                        <h3>⚠️ ${errorText}</h3>
                        <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: var(--wine-dark); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            <span class="french-text">Réessayer</span><span class="english-text">Retry</span>
                        </button>
                    </div>
                `;
            }

            showClosedOverlay(closedMessage) {
            document.getElementById('loadingState').style.display = 'none';
            
            // Update header with correct winery name from closed response
            if (closedMessage && closedMessage.wineryName) {
                document.getElementById('wineryName').textContent = closedMessage.wineryName;
                
                // Show logo only for premium wineries, otherwise show nothing
                const logoElement = document.getElementById('wineryLogo');
                if (PremiumFeatures.hasFeature(this.winery, 'logo_display') && this.winery && this.winery.logo) {
                    logoElement.innerHTML = `<img src="${this.winery.logo}" alt="${closedMessage.wineryName}">`;
                    logoElement.style.display = 'block';
                    document.querySelector('.winery-name').style.marginTop = '0px';
                } else {
                    logoElement.style.display = 'none';
                    document.querySelector('.winery-name').style.marginTop = '20px';
                }
            }

            const overlay = document.createElement('div');
            overlay.className = 'closed-overlay';
            overlay.innerHTML = `
                <div class="closed-message">
                    <h3>
                        <span class="french-text">${closedMessage.fr}</span>
                        <span class="english-text">${closedMessage.en}</span>
                    </h3>
                    <button onclick="location.reload()" class="retry-btn">
                        <span class="french-text">Vérifier à nouveau</span>
                        <span class="english-text">Check again</span>
                    </button>
                </div>
            `;
            
            // Gray out the main content and add overlay
            const mainContent = document.getElementById('mainContent');
            mainContent.style.display = 'block';
            mainContent.style.opacity = '0.3';
            mainContent.style.pointerEvents = 'none';
            
            document.querySelector('.app-container').appendChild(overlay);
        }

        }

        // Initialize the app when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.wineTastingApp = new WineTastingApp();
        });
    </script>
</body>
</html>