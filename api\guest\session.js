// api/guest/session.js - FIXED to match working menu.js pattern

import { query } from '../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        // DIRECT SUBDOMAIN EXTRACTION (same as menu.js)
        const host = req.headers.host || '';
        const winerySlug = host.split('.')[0];
        
        console.log('Session API - Host:', host);
        console.log('Session API - Winery Slug:', winerySlug);
        
        if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
            return res.status(400).json({
                success: false,
                error: 'Invalid winery subdomain'
            });
        }

        // Look up winery by slug (same as menu.js)
        const wineryResult = await query(
            'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
            [winerySlug]
        );

        if (!wineryResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: `Winery "${winerySlug}" not found or unavailable`
            });
        }

        const winery = wineryResult.rows[0];
        const { groupName, guests, plateauOrders } = req.body;
        console.log('Session creation data:', { groupName, guestCount: guests.length, plateauOrders });

        // Validate business rules
        if (!groupName || !guests || guests.length < winery.min_guests_per_group || guests.length > winery.max_guests_per_group) {
            return res.status(400).json({
                success: false,
                error: `Group size must be between ${winery.min_guests_per_group} and ${winery.max_guests_per_group} guests`
            });
        }

        // Create session
        const sessionQuery = `
            INSERT INTO tasting_sessions (winery_id, group_name, guest_count, status)
            VALUES ($1, $2, $3, 'active')
            RETURNING id, group_code
        `;
        
        const sessionResult = await query(sessionQuery, [winery.id, groupName, guests.length]);
        const session = sessionResult.rows[0];

        // Create guests and their wine orders
        for (let i = 0; i < guests.length; i++) {
            const guest = guests[i];
            
            // Insert guest
            const guestQuery = `
                INSERT INTO guests (session_id, guest_name, guest_number)
                VALUES ($1, $2, $3)
                RETURNING id
            `;
            
            const guestResult = await query(guestQuery, [session.id, guest.name, i + 1]);
            const guestId = guestResult.rows[0].id;

            // Insert wine orders for this guest
            if (guest.wines && guest.wines.length > 0) {
                for (const wineId of guest.wines) {
                    const wineOrderQuery = `
                        INSERT INTO wine_orders (session_id, guest_id, wine_id, status)
                        VALUES ($1, $2, $3, 'ordered')
                    `;
                    await query(wineOrderQuery, [session.id, guestId, wineId]);
                }
            }
        }

        // Create plateau orders (group-based)
        console.log('Processing plateau orders:', plateauOrders);
        if (plateauOrders && plateauOrders.length > 0) {
            console.log('Inserting', plateauOrders.length, 'plateau orders');
            for (const plateauOrder of plateauOrders) {
                const plateauOrderQuery = `
                    INSERT INTO plateau_orders (session_id, plateau_id, plateau_count, status)
                    VALUES ($1, $2, $3, 'ordered')
                `;
                await query(plateauOrderQuery, [session.id, plateauOrder.plateauId, plateauOrder.count]);
            }
        }

        // Create session lock
        const lockQuery = `
            INSERT INTO session_locks (session_id, is_locked, locked_by)
            VALUES ($1, true, 'system')
        `;
        await query(lockQuery, [session.id]);

        res.status(200).json({
            success: true,
            session: {
                id: session.id,
                groupCode: session.group_code,
                redirectUrl: `/session?code=${session.group_code}`
            }
        });

    } catch (error) {
        console.error('Session creation error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create session'
        });
    }
}