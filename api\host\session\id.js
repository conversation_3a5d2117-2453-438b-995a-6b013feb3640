// api/host/session/id.js - CLEAN VERSION - No syntax errors
import { query } from '../../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        const { id } = req.query;
        
        if (!id) {
            return res.status(400).json({
                success: false,
                error: 'Session ID is required'
            });
        }

        // Direct subdomain extraction (consistent with working APIs)
        const host = req.headers.host || '';
        const winerySlug = host.split('.')[0];
        
        if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
            return res.status(400).json({
                success: false,
                error: 'Invalid winery subdomain'
            });
        }

        // Get winery
        const wineryResult = await query(
            'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
            [winerySlug]
        );

        if (!wineryResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: 'Winery not found'
            });
        }

        const winery = wineryResult.rows[0];

        // Get session with lock status
        const sessionResult = await query(`
            SELECT ts.*, sl.is_locked, sl.locked_by
            FROM tasting_sessions ts
            LEFT JOIN session_locks sl ON ts.id = sl.session_id
            WHERE ts.id = $1 AND ts.winery_id = $2
        `, [id, winery.id]);

        if (!sessionResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: 'Session not found'
            });
        }

        const sessionRow = sessionResult.rows[0];

        // Get guests with wine orders - ADMIN PANEL ORDER MATCHING
        const guestsResult = await query(`
            SELECT 
                g.id,
                g.guest_name,
                g.guest_number,
                g.created_at,
                COALESCE(
                    json_agg(
                        json_build_object(
                            'orderId', wo.id,
                            'wineId', wo.wine_id,
                            'wineName', w.name,
                            'wine_category', w.category,
                            'category_name_fr', wc.name_fr,
                            'category_name_en', wc.name_en,
                            'status', wo.status,
                            'orderedAt', wo.ordered_at,
                            'servedAt', wo.served_at
                        ) ORDER BY w.sort_order
                    ) FILTER (WHERE wo.id IS NOT NULL), 
                    '[]'
                ) as wine_orders
            FROM guests g
            LEFT JOIN wine_orders wo ON g.id = wo.guest_id
            LEFT JOIN wines w ON wo.wine_id = w.id
            LEFT JOIN wine_categories wc ON w.category = wc.category_code
            WHERE g.session_id = $1
            GROUP BY g.id, g.guest_name, g.guest_number, g.created_at
            ORDER BY g.guest_number
        `, [id]);

        // Get plateau orders
        const plateauResult = await query(`
            SELECT 
                po.id,
                po.plateau_count,
                po.status,
                po.ordered_at,
                po.served_at,
                p.name_fr,
                p.name_en,
                p.description_fr,
                p.description_en
            FROM plateau_orders po
            JOIN plateaux p ON po.plateau_id = p.id
            WHERE po.session_id = $1
            ORDER BY po.ordered_at
        `, [id]);

        // Transform session data to camelCase for frontend
        const session = {
            id: sessionRow.id,
            groupCode: sessionRow.group_code,
            groupName: sessionRow.group_name,
            guestCount: sessionRow.guest_count,
            status: sessionRow.status,
            createdAt: sessionRow.created_at,
            completedAt: sessionRow.completed_at,
            isLocked: sessionRow.is_locked || false,
            lockedBy: sessionRow.locked_by,
            hostNotes: sessionRow.host_notes
        };

        // Transform guests to camelCase
        const guests = guestsResult.rows.map(guest => ({
            id: guest.id,
            guestName: guest.guest_name,
            guestNumber: guest.guest_number,
            createdAt: guest.created_at,
            wineOrders: guest.wine_orders
        }));

        // Transform plateau orders to camelCase
        const plateauOrders = plateauResult.rows.map(plateau => ({
            id: plateau.id,
            plateauCount: plateau.plateau_count,
            status: plateau.status,
            orderedAt: plateau.ordered_at,
            servedAt: plateau.served_at,
            nameFr: plateau.name_fr,
            nameEn: plateau.name_en,
            descriptionFr: plateau.description_fr,
            descriptionEn: plateau.description_en
        }));

        res.status(200).json({
            success: true,
            session: session,
            guests: guests,
            plateauOrders: plateauOrders,
            winery: {
                id: winery.id,
                name: winery.name,
                slug: winery.slug,
                subscription_tier: winery.subscription_tier,
                logo: winery.logo
            }
        });

    } catch (error) {
        console.error('Host session error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to load session'
        });
    }
}