<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plateau Management - Admin</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .admin-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #722f37;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #722f37;
            text-decoration: none;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #722f37;
            margin-bottom: 0.3rem;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #722f37;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a252a;
        }
        
        .btn-secondary {
            background: #e9ecef;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #dee2e6;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .plateau-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .plateau-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .plateau-card:hover {
            transform: translateY(-5px);
        }
        
        .plateau-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .plateau-image {
            width: 80px;
            height: 60px;
            background: linear-gradient(135deg, #f9ca24, #f0932b);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .plateau-info {
            flex: 1;
            margin-left: 1rem;
        }
        
        .plateau-names {
            margin-bottom: 0.5rem;
        }
        
        .plateau-name-fr {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.2rem;
        }
        
        .plateau-name-en {
            font-size: 1rem;
            color: #722f37;
            font-style: italic;
        }
        
        .plateau-price {
            font-weight: 600;
            color: #28a745;
            font-size: 1.1rem;
        }
        
        .plateau-descriptions {
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .description-fr {
            margin-bottom: 0.5rem;
            color: #333;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .description-en {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
            font-style: italic;
        }
        
        .plateau-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .plateau-quantity {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .quantity-range {
            background: #17a2b8;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .plateau-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-toggle {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .status-toggle.active {
            background: #28a745;
        }
        
        .status-toggle::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
        }
        
        .status-toggle.active::after {
            transform: translateX(20px);
        }
        
        .plateau-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .edit-btn { background: #17a2b8; color: white; }
        .edit-btn:hover { background: #138496; }
        .delete-btn { background: #dc3545; color: white; }
        .delete-btn:hover { background: #c82333; }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .plateau-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: none;
        }
        
        .plateau-form.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #722f37;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #722f37;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="admin-logo">
            <span>🧀</span>
            Plateau Management
        </div>
        <div class="breadcrumb">
            <a href="/admin/index.html">Dashboard</a>
            <span>→</span>
            <span id="currentWineryName">Plateau Management</span>
        </div>
    </div>
    
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <div class="page-title" id="pageTitle">Plateau Management</div>
                <div class="page-subtitle" id="pageSubtitle">Manage food platters for winery</div>
            </div>
            <div style="display: flex; gap: 1rem;">
                <button class="btn btn-secondary" onclick="window.location.href='/admin/index.html'">
                    ← Back to Dashboard
                </button>
                <button class="btn btn-primary" onclick="plateauManager.showAddForm()">
                    + Add Plateau
                </button>
            </div>
        </div>
        
        <!-- Plateau Add/Edit Form -->
        <div class="plateau-form" id="plateauForm">
            <h3 id="formTitle">Add New Plateau</h3>
            
            <form id="plateauFormElement">
                <input type="hidden" id="plateauId" name="plateauId">
                <input type="hidden" id="wineryId" name="wineryId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="plateauNameFr" class="form-label">Name (French) *</label>
                        <input type="text" id="plateauNameFr" name="name_fr" class="form-input" required placeholder="e.g. Plateau de Charcuterie">
                    </div>
                    
                    <div class="form-group">
                        <label for="plateauNameEn" class="form-label">Name (English) *</label>
                        <input type="text" id="plateauNameEn" name="name_en" class="form-input" required placeholder="e.g. Charcuterie Board">
                    </div>
                    
                    <div class="form-group">
                        <label for="plateauPrice" class="form-label">Price ($)</label>
                        <input type="number" id="plateauPrice" name="price" class="form-input" min="0" step="0.01" placeholder="15.99">
                    </div>
                    
                    <div class="form-group">
                        <label for="plateauSort" class="form-label">Sort Order</label>
                        <input type="number" id="plateauSort" name="sort_order" class="form-input" min="0" placeholder="0">
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="plateauDescriptionFr" class="form-label">Description (French)</label>
                        <textarea id="plateauDescriptionFr" name="description_fr" class="form-input form-textarea" placeholder="Description détaillée en français..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="plateauDescriptionEn" class="form-label">Description (English)</label>
                        <textarea id="plateauDescriptionEn" name="description_en" class="form-input form-textarea" placeholder="Detailed English description..."></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="plateauManager.hideForm()">Cancel</button>
                    <button type="submit" class="btn btn-success" id="saveBtn">
                        <span id="saveText">Save Plateau</span>
                        <span id="saveSpinner" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Plateau Grid -->
        <div id="plateauGrid" class="plateau-grid">
            <div class="loading" style="grid-column: 1 / -1; text-align: center; padding: 2rem;"></div>
        </div>
    </div>

    <script>
        // Plateau Management Logic
        class PlateauManager {
            constructor() {
                this.currentWinery = null;
                this.plateaux = [];
                this.isEditing = false;
                this.init();
            }
            
            async init() {
                // Check authentication
                if (!this.isAuthenticated()) {
                    window.location.href = '/admin/auth.html';
                    return;
                }
                
                // Get winery from URL
                const urlParams = new URLSearchParams(window.location.search);
                const wineryId = urlParams.get('winery');
                
                if (!wineryId) {
                    alert('No winery specified');
                    window.location.href = '/admin/index.html';
                    return;
                }
                
                // Load winery and plateaux
                await this.loadWinery(wineryId);
                await this.loadPlateaux();
                
                // Setup form submission
                document.getElementById('plateauFormElement').addEventListener('submit', (e) => this.handleSubmit(e));
            }
            
            isAuthenticated() {
                const token = localStorage.getItem('adminAuth');
                const authTime = localStorage.getItem('adminAuthTime');
                
                if (!token || !authTime) {
                    return false;
                }
                
                const oneHour = 60 * 60 * 1000;
                const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
                
                return !isExpired;
            }
            
            async loadWinery(wineryId) {
                try {
                    // LOAD ACTUAL WINERY FROM DATABASE
                    const response = await fetch('/api/admin/settings', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.currentWinery = data.wineries.find(w => w.id == wineryId);
                    }
                    
                    if (this.currentWinery) {
                        document.getElementById('pageTitle').textContent = `Wine Management - ${this.currentWinery.name}`;
                        document.getElementById('pageSubtitle').textContent = `Manage wine selection for ${this.currentWinery.name}`;
                        document.getElementById('currentWineryName').textContent = this.currentWinery.name;
                        document.getElementById('wineryId').value = this.currentWinery.id;
                    } else {
                        alert('Winery not found');
                        window.location.href = '/admin/index.html';
                    }
                } catch (error) {
                    console.error('Error loading winery:', error);
                    alert('Failed to load winery');
                    window.location.href = '/admin/index.html';
                }
            }
            
            async loadPlateaux() {
                try {
                    const response = await fetch(`/api/admin/plateaux?winery=${this.currentWinery.id}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.plateaux = data.plateaux || [];
                    } else {
                        console.error('Failed to load plateaux:', response.status);
                        this.plateaux = [];
                    }
                    
                    this.renderPlateaux();
                } catch (error) {
                    console.error('Error loading plateaux:', error);
                    this.plateaux = [];
                    this.renderPlateaux();
                }
            }
            
            renderPlateaux() {
                const grid = document.getElementById('plateauGrid');
                
                if (this.plateaux.length === 0) {
                    grid.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🧀</div>
                            <h3>No plateaux added yet</h3>
                            <p>Start building your food selection by adding your first plateau.</p>
                            <button class="btn btn-primary" onclick="plateauManager.showAddForm()">Add First Plateau</button>
                        </div>
                    `;
                    return;
                }
                
                grid.innerHTML = this.plateaux.map(plateau => `
                    <div class="plateau-card">
                        <div class="plateau-actions">
                            <button class="action-btn edit-btn" onclick="plateauManager.editPlateau(${plateau.id})" title="Edit plateau">
                                ✏️
                            </button>
                            <button class="action-btn delete-btn" onclick="plateauManager.deletePlateau(${plateau.id})" title="Delete plateau">
                                🗑️
                            </button>
                        </div>
                        
                        <div class="plateau-header">
                            <div class="plateau-image">🧀</div>
                            <div class="plateau-info">
                                <div class="plateau-names">
                                    <div class="plateau-name-fr">${plateau.name_fr}</div>
                                    <div class="plateau-name-en">${plateau.name_en}</div>
                                </div>
                                <div class="plateau-price">$${plateau.price || '0.00'}</div>
                            </div>
                        </div>
                        
                        ${(plateau.description_fr || plateau.description_en) ? `
                            <div class="plateau-descriptions">
                                ${plateau.description_fr ? `<div class="description-fr">${plateau.description_fr}</div>` : ''}
                                ${plateau.description_en ? `<div class="description-en">${plateau.description_en}</div>` : ''}
                            </div>
                        ` : ''}
                        
                        <div class="plateau-meta">
                            <div class="plateau-quantity">
                                <span>Quantity per group:</span>
                                <div class="quantity-range">0-8</div>
                            </div>
                            <div class="plateau-status">
                                <span>${plateau.is_available ? 'Available' : 'Unavailable'}</span>
                                <div class="status-toggle ${plateau.is_available ? 'active' : ''}" 
                                     onclick="plateauManager.toggleAvailability(${plateau.id})"></div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            showAddForm() {
                this.isEditing = false;
                document.getElementById('formTitle').textContent = 'Add New Plateau';
                document.getElementById('saveText').textContent = 'Save Plateau';
                document.getElementById('plateauFormElement').reset();
                document.getElementById('plateauId').value = '';
                document.getElementById('wineryId').value = this.currentWinery.id;
                document.getElementById('plateauForm').classList.add('active');
                document.getElementById('plateauNameFr').focus();
            }
            
            hideForm() {
                document.getElementById('plateauForm').classList.remove('active');
            }
            
            editPlateau(plateauId) {
                const plateau = this.plateaux.find(p => p.id === plateauId);
                if (!plateau) return;
                
                this.isEditing = true;
                document.getElementById('formTitle').textContent = 'Edit Plateau';
                document.getElementById('saveText').textContent = 'Update Plateau';
                
                // FIXED: Populate form with correct field names
                document.getElementById('plateauId').value = plateau.id;
                document.getElementById('wineryId').value = this.currentWinery.id;
                document.getElementById('plateauNameFr').value = plateau.name_fr;
                document.getElementById('plateauNameEn').value = plateau.name_en;
                document.getElementById('plateauPrice').value = plateau.price || '';
                document.getElementById('plateauSort').value = plateau.sort_order || '';
                document.getElementById('plateauDescriptionFr').value = plateau.description_fr || '';
                document.getElementById('plateauDescriptionEn').value = plateau.description_en || '';
                
                document.getElementById('plateauForm').classList.add('active');
                document.getElementById('plateauNameFr').focus();
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const saveBtn = document.getElementById('saveBtn');
                const saveText = document.getElementById('saveText');
                const saveSpinner = document.getElementById('saveSpinner');
                
                saveBtn.disabled = true;
                saveText.style.display = 'none';
                saveSpinner.style.display = 'inline-block';
                
                try {
                    const formData = new FormData(e.target);
                    const plateauData = Object.fromEntries(formData);
                    
                    // FIXED: Match wines.js pattern exactly
                    const method = this.isEditing ? 'PATCH' : 'POST';
                    const url = this.isEditing ? 
                        `/api/admin/plateaux?id=${plateauData.plateauId}` :
                        '/api/admin/plateaux';
                    
                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        },
                        body: JSON.stringify(plateauData)
                    });
                    
                    if (response.ok) {
                        this.hideForm();
                        await this.loadPlateaux();
                        alert(this.isEditing ? 'Plateau updated successfully!' : 'Plateau added successfully!');
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to save plateau'}`);
                    }
                } catch (error) {
                    console.error('Error saving plateau:', error);
                    alert('Failed to save plateau. Please try again.');
                } finally {
                    saveBtn.disabled = false;
                    saveText.style.display = 'inline';
                    saveSpinner.style.display = 'none';
                }
            }
            
            async toggleAvailability(plateauId) {
                try {
                    // FIXED: Keep query parameter pattern (this one works in wines)
                    const response = await fetch(`/api/admin/plateaux?action=toggle&id=${plateauId}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        alert(data.message);
                        await this.loadPlateaux();
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to update plateau availability'}`);
                    }
                } catch (error) {
                    console.error('Error toggling availability:', error);
                    alert('Failed to update plateau availability');
                }
            }
            
            async deletePlateau(plateauId) {
                const plateau = this.plateaux.find(p => p.id === plateauId);
                if (!plateau) return;
                
                if (!confirm(`Are you sure you want to delete "${plateau.name_fr}"?\n\nThis will make it unavailable for future orders.`)) {
                    return;
                }
                
                try {
                    // CHANGED: Use query parameter instead of URL path
                    const response = await fetch(`/api/admin/plateaux?id=${plateauId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        alert(data.message);
                        await this.loadPlateaux();
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to delete plateau'}`);
                    }
                } catch (error) {
                    console.error('Error deleting plateau:', error);
                    alert('Failed to delete plateau');
                }
            }
        }
        
        // Initialize plateau manager
        const plateauManager = new PlateauManager();
    </script>
</body>
</html>