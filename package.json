{"name": "wine-tasting-app", "version": "2.0.0", "type": "module", "description": "Serverless Wine Tasting Management Platform", "main": "api/health.js", "scripts": {"test": "echo \"No tests specified\"", "test:visual:reference": "backstop reference", "test:visual": "backstop test", "test:visual:approve": "backstop approve", "test:visual:report": "backstop openReport"}, "dependencies": {"@vercel/blob": "^2.0.0", "dotenv": "^16.3.1", "nodemailer": "^7.0.6", "pg": "^8.11.3"}, "devDependencies": {"backstopjs": "^6.3.25", "vercel": "^32.0.0"}, "engines": {"node": "20.x"}}