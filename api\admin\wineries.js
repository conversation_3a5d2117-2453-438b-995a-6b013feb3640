// api/admin/wineries.js
// Simple redirect to settings endpoint for backward compatibility

export default async function handler(req, res) {
  // Redirect all requests to settings endpoint
  const { method, query, body, headers } = req;
  
  try {
    // Forward the request to settings endpoint
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3000';
      
    const settingsUrl = `${baseUrl}/api/admin/settings${query ? '?' + new URLSearchParams(query).toString() : ''}`;
    
    const response = await fetch(settingsUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': headers.authorization || ''
      },
      ...(method !== 'GET' && method !== 'HEAD' ? { body: JSON.stringify(body) } : {})
    });
    
    const data = await response.json();
    
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Wineries redirect error:', error);
    res.status(500).json({
      success: false,
      error: 'Service redirect failed'
    });
  }
}