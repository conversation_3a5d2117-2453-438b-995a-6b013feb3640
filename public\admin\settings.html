<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winery Management - Admin</title>
    <link rel="stylesheet" href="../shared/theme.css">
    <script src="../shared/premiumFeatures.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .admin-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #722f37;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #722f37;
            text-decoration: none;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #722f37;
            margin-bottom: 0.3rem;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary { background: #722f37; color: white; }
        .btn-secondary { background: #e9ecef; color: #333; }
        .btn-success { background: #28a745; color: white; }
        
        .winery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .winery-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .winery-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .winery-image {
            width: 80px;
            height: 80px;
            background: #8b0000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .winery-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .winery-info {
            flex: 1;
            margin-left: 1rem;
        }
        
        .winery-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.2rem;
            max-width: 200px;   /* sets the limit */
            overflow-wrap: break-word; /* modern equivalent */
            white-space: normal;  /* ensures text wraps to the next line */
        }
        
        .winery-slug {
            font-size: 0.9rem;
            color: #666;
            font-family: monospace;
            margin-bottom: 0.5rem;
        }
        
        .winery-tier {
            font-weight: 600;
            color: #28a745;
            font-size: 0.9rem;
        }
        
        .winery-details {
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .detail-row {
            margin-bottom: 0.5rem;
            color: #333;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .winery-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .winery-rules {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .rule-badge {
            background: #17a2b8;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .winery-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-toggle {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .status-toggle.active {
            background: #28a745;
        }
        
        .status-toggle::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
        }
        
        .status-toggle.active::after {
            transform: translateX(20px);
        }
        
        .winery-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            z-index: 100;
        }
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .edit-btn { background: #17a2b8; color: white; }
        .delete-btn { background: #dc3545; color: white; }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .winery-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: none;
        }
        
        .winery-form.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #722f37;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-select {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        
        /* Logo Upload Styles */
        .logo-upload-container {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .logo-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .logo-preview.hidden {
            display: none;
        }

        .logo-preview-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            border: 2px solid #e9ecef;
        }

        .logo-preview-info {
            flex: 1;
        }

        .logo-preview-name {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
        }

        .logo-preview-size {
            font-size: 0.8rem;
            color: #666;
        }

        .logo-remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logo-remove-btn:hover {
            background: #c82333;
        }

        .form-help {
            color: #666;
            font-size: 0.85rem;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #722f37;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        /* Closed Messages Section */
        .closed-messages-section {
            margin-top: 2rem;
        }

        .section-title {
            color: #722f37;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        /* Operating Hours Section */
        .operating-hours-section {
            margin-top: 2rem;
        }

        .hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .day-hours {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 1rem;
            border: 1px solid #e9ecef;
        }

        .day-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .hours-control {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .time-inputs {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.9rem;
            transition: opacity 0.3s ease;
        }

        .time-inputs input[type="time"] {
            width: 70px;
            padding: 0.3rem 0.4rem;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.85rem;
        }

        .time-separator {
            color: #666;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .closed-checkbox {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="admin-logo">
            <span>🏛️</span>
            Winery Management
        </div>
        <div class="breadcrumb">
            <a href="/admin/index.html">Dashboard</a>
            <span>→</span>
            <span>Wineries</span>
        </div>
    </div>
    
    <div class="container">
        <div class="page-header">
            <div>
                <div class="page-title">Winery Management</div>
                <div class="page-subtitle">Manage all wineries and their configurations</div>
            </div>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <button class="btn btn-secondary" onclick="window.location.href='/admin/index.html'">
                    ← Admin Dashboard
                </button>
                <div class="filter-controls" style="display: flex; gap: 0.5rem;">
                    <button class="btn btn-secondary active" id="activeFilter" onclick="wineryManager.filterWineries('active')">Active</button>
                    <button class="btn btn-secondary" id="inactiveFilter" onclick="wineryManager.filterWineries('inactive')">Archived</button>
                </div>
                <button class="btn btn-primary" onclick="wineryManager.showAddForm()">
                    + Add Winery
                </button>
            </div>
        </div>
        
        <div class="winery-form" id="wineryForm">
            <h3 id="formTitle">Add New Winery</h3>
            
            <form id="wineryFormElement">
                <input type="hidden" id="wineryId" name="wineryId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="wineryName" class="form-label">Winery Name *</label>
                        <input type="text" id="wineryName" name="name" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="winerySlug" class="form-label">Subdomain Slug *</label>
                        <input type="text" id="winerySlug" name="slug" class="form-input" required placeholder="Winery name loading">
                        <small style="color: #666; margin-top: 0.3rem;">Will create: <span id="slugPreview">subdomain.ezvino.app</span></small>
                    </div>
                    
                    <div class="form-group">
                        <label for="wineryEmail" class="form-label">Contact Email</label>
                        <input type="email" id="wineryEmail" name="email" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="wineryPhone" class="form-label">Phone Number</label>
                        <input type="tel" id="wineryPhone" name="phone" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="wineryLogo" class="form-label">Winery Logo</label>
                        <div class="logo-upload-container">
                            <input type="file" id="wineryLogo" accept="image/png,image/jpg,image/jpeg" class="form-input">
                            <div id="logoPreview" class="logo-preview hidden">
                                <img id="logoImage" class="logo-preview-image">
                                <div class="logo-preview-info">
                                    <div class="logo-preview-name" id="logoName"></div>
                                    <div class="logo-preview-size" id="logoSize"></div>
                                </div>
                                <button type="button" onclick="clearLogo()" class="logo-remove-btn">Remove</button>
                            </div>
                            <small class="form-help">PNG or JPG, max 2MB. Will be resized to 180x180px.</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="wineryTier" class="form-label">Subscription Tier</label>
                        <select id="wineryTier" name="subscription_tier" class="form-select">
                            <option value="freemium">Freemium</option>
                            <option value="premium">Premium</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="maxGuests" class="form-label">Max Guests per Group</label>
                        <input type="number" id="maxGuests" name="max_guests_per_group" class="form-input" min="2" max="8" value="8">
                    </div>
                    
                    <div class="form-group">
                        <label for="minGuests" class="form-label">Min Guests per Group</label>
                        <input type="number" id="minGuests" name="min_guests_per_group" class="form-input" min="1" max="8" value="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="winesPerGuest" class="form-label">Wines per Guest</label>
                        <input type="number" id="winesPerGuest" name="wines_per_guest" class="form-input" min="1" max="10" value="4">
                    </div>
                    
                    <div class="form-group">
                        <label for="maxPlateaux" class="form-label">Max Plateaux per Group</label>
                        <input type="number" id="maxPlateaux" name="max_plateaux_per_group" class="form-input" min="0" max="10" value="2">
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="wineryAddress" class="form-label">Address</label>
                        <textarea id="wineryAddress" name="address" class="form-input form-textarea"></textarea>
                    </div>
                </div>
                
                <!-- Operating Hours Section -->
                <div class="operating-hours-section">
                    <h3 class="section-title">Operating Hours</h3>
                    
                    <div class="form-group" style="max-width: 300px;">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select id="timezone" name="timezone" class="form-select">
                            <option value="America/Montreal">America/Montreal (UTC-5/-4)</option>
                            <option value="America/Toronto">America/Toronto (UTC-5/-4)</option>
                            <option value="America/New_York">America/New_York (UTC-5/-4)</option>
                            <option value="America/Chicago">America/Chicago (UTC-6/-5)</option>
                            <option value="America/Denver">America/Denver (UTC-7/-6)</option>
                            <option value="America/Los_Angeles">America/Los_Angeles (UTC-8/-7)</option>
                            <option value="America/Vancouver">America/Vancouver (UTC-8/-7)</option>
                            <option value="Europe/London">Europe/London (UTC+0/+1)</option>
                            <option value="Europe/Paris">Europe/Paris (UTC+1/+2)</option>
                            <option value="Europe/Berlin">Europe/Berlin (UTC+1/+2)</option>
                            <option value="Australia/Sydney">Australia/Sydney (UTC+10/+11)</option>
                            <option value="Pacific/Auckland">Pacific/Auckland (UTC+12/+13)</option>
                            <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
                        </select>
                    </div>
                    
                    <div class="hours-grid">
                        <div class="day-hours">
                            <div class="day-label">Monday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="monday-closed" onchange="toggleDayHours('monday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="monday-times">
                                    <input type="time" id="monday-open" value="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="monday-close" value="17:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Tuesday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="tuesday-closed" onchange="toggleDayHours('tuesday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="tuesday-times">
                                    <input type="time" id="tuesday-open" value="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="tuesday-close" value="17:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Wednesday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="wednesday-closed" onchange="toggleDayHours('wednesday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="wednesday-times">
                                    <input type="time" id="wednesday-open" value="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="wednesday-close" value="17:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Thursday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="thursday-closed" onchange="toggleDayHours('thursday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="thursday-times">
                                    <input type="time" id="thursday-open" value="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="thursday-close" value="17:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Friday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="friday-closed" onchange="toggleDayHours('friday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="friday-times">
                                    <input type="time" id="friday-open" value="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="friday-close" value="17:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Saturday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="saturday-closed" onchange="toggleDayHours('saturday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="saturday-times">
                                    <input type="time" id="saturday-open" value="10:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="saturday-close" value="18:00">
                                </div>
                            </div>
                        </div>
                        
                        <div class="day-hours">
                            <div class="day-label">Sunday</div>
                            <div class="hours-control">
                                <label class="closed-checkbox">
                                    <input type="checkbox" id="sunday-closed" onchange="toggleDayHours('sunday')">
                                    Closed
                                </label>
                                <div class="time-inputs" id="sunday-times">
                                    <input type="time" id="sunday-open" value="11:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" id="sunday-close" value="16:00">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Closed Messages Section -->
                <div class="closed-messages-section">
                    <h3 class="section-title">Closed Hours Messages</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="closedMessageFr" class="form-label">French Message</label>
                            <textarea id="closedMessageFr" name="closed_message_fr" class="form-input form-textarea" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="closedMessageEn" class="form-label">English Message</label>
                            <textarea id="closedMessageEn" name="closed_message_en" class="form-input form-textarea" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="wineryManager.hideForm()">Cancel</button>
                    <button type="submit" class="btn btn-success" id="saveBtn">
                        <span id="saveText">Save Winery</span>
                        <span id="saveSpinner" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
        
        <div id="wineryGrid" class="winery-grid">
            <div class="loading" style="grid-column: 1 / -1; text-align: center; padding: 2rem;"></div>
        </div>
    </div>

    <script>
        // Operating Hours Data Functions
        function collectOperatingHours() {
            const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            const hours = {};
            
            days.forEach(day => {
                const closed = document.getElementById(`${day}-closed`).checked;
                const open = document.getElementById(`${day}-open`).value;
                const close = document.getElementById(`${day}-close`).value;
                
                hours[day] = {
                    closed: closed,
                    open: closed ? '09:00' : open,
                    close: closed ? '17:00' : close
                };
            });
            
            return hours;
        }

        function populateOperatingHours(operationalHours) {
            const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            
            days.forEach(day => {
                const dayHours = operationalHours?.[day];
                if (dayHours) {
                    document.getElementById(`${day}-closed`).checked = dayHours.closed || false;
                    document.getElementById(`${day}-open`).value = dayHours.open || '09:00';
                    document.getElementById(`${day}-close`).value = dayHours.close || '17:00';
                    toggleDayHours(day); // Apply visual state
                }
            });
        }

        // Operating Hours Helper Function
        function toggleDayHours(day) {
            const checkbox = document.getElementById(`${day}-closed`);
            const timeInputs = document.getElementById(`${day}-times`);
            
            if (checkbox.checked) {
                timeInputs.style.opacity = '0.5';
                timeInputs.style.pointerEvents = 'none';
            } else {
                timeInputs.style.opacity = '1';
                timeInputs.style.pointerEvents = 'auto';
            }
        }

        // Logo Upload Functions
        function clearLogo() {
            document.getElementById('wineryLogo').value = '';
            document.getElementById('logoPreview').classList.add('hidden');
        }

        function handleLogoPreview(file) {
            if (!file) return;
            
            // Validate file type
            const validTypes = ['image/png', 'image/jpeg', 'image/jpg'];
            if (!validTypes.includes(file.type)) {
                alert('Please select a PNG or JPG image.');
                clearLogo();
                return;
            }
            
            // Validate file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB.');
                clearLogo();
                return;
            }
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('logoImage').src = e.target.result;
                document.getElementById('logoName').textContent = file.name;
                document.getElementById('logoSize').textContent = `${(file.size / 1024).toFixed(1)} KB`;
                document.getElementById('logoPreview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }

        // Add event listener when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('wineryLogo').addEventListener('change', function(e) {
                handleLogoPreview(e.target.files[0]);
            });
        });

        class WineryManager {
            constructor() {
                this.wineries = [];
                this.isEditing = false;
                this.init();
            }
            
            async init() {
                // Check authentication (like wines.html)
                if (!this.isAuthenticated()) {
                    window.location.href = '/admin/auth.html';
                    return;
                }
                
                await this.loadWineries();
                document.getElementById('wineryFormElement').addEventListener('submit', (e) => this.handleSubmit(e));
                
                // Auto-generate slug from name
                document.getElementById('wineryName').addEventListener('input', (e) => {
                    if (!this.isEditing) {
                        const slug = e.target.value.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim();
                        document.getElementById('winerySlug').value = slug;
                        this.updateSlugPreview(slug);
                    }
                });
                
                // Update slug preview on direct input
                document.getElementById('winerySlug').addEventListener('input', (e) => {
                    this.updateSlugPreview(e.target.value);
                });
            }
            
            isAuthenticated() {
                const token = localStorage.getItem('adminAuth');
                const authTime = localStorage.getItem('adminAuthTime');
                
                if (!token || !authTime) {
                    return false;
                }
                
                const oneHour = 60 * 60 * 1000;
                const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
                
                return !isExpired;
            }
            
            updateSlugPreview(slug) {
                const preview = document.getElementById('slugPreview');
                preview.textContent = slug ? `${slug}.ezvino.app` : 'subdomain.ezvino.app';
            }
            
            
            async loadWineries() {
                try {
                    // FIXED: Use proper authentication like wines.html
                    const response = await fetch('/api/admin/settings', {
                        headers: { 
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}` 
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.wineries = data.wineries || [];
                        this.currentFilter = 'active';
                    } else {
                        console.error('Failed to load wineries:', response.status);
                        this.wineries = [];
                    }
                    this.renderWineries();
                } catch (error) {
                    console.error('Error loading wineries:', error);
                    this.wineries = [];
                    this.renderWineries();
                }
            }

            filterWineries(type) {
                this.currentFilter = type;
                
                // Update button states
                document.getElementById('activeFilter').classList.toggle('active', type === 'active');
                document.getElementById('inactiveFilter').classList.toggle('active', type === 'inactive');
                
                // Re-render with current filter
                this.renderWineries();
            }
            
            renderWineries() {
                const grid = document.getElementById('wineryGrid');
                
                // Apply current filter (default to 'active')
                const filter = this.currentFilter || 'active';
                const filtered = filter === 'active' 
                    ? this.wineries.filter(w => w.is_active)
                    : this.wineries.filter(w => !w.is_active);

                if (filtered.length === 0) {
                    grid.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🏛️</div>
                            <h3>No wineries added yet</h3>
                            <p>Create your first winery to get started.</p>
                            <button class="btn btn-primary" onclick="wineryManager.showAddForm()">Add First Winery</button>
                        </div>
                    `;
                    return;
                }
                
                grid.innerHTML = filtered.map(winery => `
                    <div class="winery-card">
                        <div class="winery-actions">
                            <button class="action-btn edit-btn" onclick="wineryManager.editWinery(${winery.id})">✏️</button>
                            <button class="action-btn delete-btn" onclick="wineryManager.deleteWinery(${winery.id})">🗑️</button>
                        </div>
                        
                        <div class="winery-header">
                            <div class="winery-image">
                                ${winery.logo ? 
                                    `<img src="${winery.logo}" alt="${winery.name}">` : 
                                    this.getWineryInitials(winery.name)
                                }
                            </div>
                            <div class="winery-info">
                                <div class="winery-name">${winery.name}</div>
                                <div class="winery-slug">${winery.slug}.ezvino.app</div>
                                <div class="winery-tier">${winery.subscription_tier}</div>
                            </div>
                        </div>
                        
                        <div class="winery-details">
                            ${winery.email ? `<div class="detail-row">📧 ${winery.email}</div>` : ''}
                            ${winery.phone ? `<div class="detail-row">📞 ${winery.phone}</div>` : ''}
                            ${winery.address ? `<div class="detail-row">📍 ${winery.address}</div>` : ''}
                        </div>
                        
                        <div class="winery-meta">
                            <div class="winery-rules">
                                <span>Rules:</span>
                                <div class="rule-badge">${winery.min_guests_per_group}-${winery.max_guests_per_group} guests</div>
                                <div class="rule-badge">${winery.wines_per_guest} wines/guest</div>
                            </div>
                            <div class="winery-status">
                                <span>${winery.is_active ? 'Active' : 'Inactive'}</span>
                                <div class="status-toggle ${winery.is_active ? 'active' : ''}" 
                                     onclick="wineryManager.toggleStatus(${winery.id})"></div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            showAddForm() {
                this.isEditing = false;
                document.getElementById('formTitle').textContent = 'Add New Winery';
                document.getElementById('saveText').textContent = 'Save Winery';
                document.getElementById('wineryFormElement').reset();
                document.getElementById('wineryId').value = '';
                document.getElementById('wineryForm').classList.add('active');
                this.updateSlugPreview('');
                document.getElementById('wineryName').focus();
            }
            
            hideForm() {
                document.getElementById('wineryForm').classList.remove('active');
            }
            
            editWinery(wineryId) {
                const winery = this.wineries.find(w => w.id === wineryId);
                if (!winery) return;
                
                this.isEditing = true;
                document.getElementById('formTitle').textContent = 'Edit Winery';
                document.getElementById('saveText').textContent = 'Update Winery';
                
                // Populate basic form fields
                document.getElementById('wineryId').value = winery.id;
                document.getElementById('wineryName').value = winery.name;
                document.getElementById('winerySlug').value = winery.slug;
                document.getElementById('wineryEmail').value = winery.email || '';
                document.getElementById('wineryPhone').value = winery.phone || '';
                document.getElementById('wineryTier').value = winery.subscription_tier || 'freemium';
                document.getElementById('maxGuests').value = winery.max_guests_per_group || 8;
                document.getElementById('minGuests').value = winery.min_guests_per_group || 1;
                document.getElementById('winesPerGuest').value = winery.wines_per_guest || 4;
                document.getElementById('maxPlateaux').value = winery.max_plateaux_per_group || 2;
                document.getElementById('wineryAddress').value = winery.address || '';
                
                // Populate operating hours
                populateOperatingHours(winery.operational_hours);
                
                // Populate timezone and closed messages
                document.getElementById('timezone').value = winery.timezone || 'America/Montreal';
                document.getElementById('closedMessageFr').value = winery.closed_message_fr || '';
                document.getElementById('closedMessageEn').value = winery.closed_message_en || '';
                
                this.updateSlugPreview(winery.slug);
                document.getElementById('wineryForm').classList.add('active');
                document.getElementById('wineryName').focus();
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const saveBtn = document.getElementById('saveBtn');
                const saveText = document.getElementById('saveText');
                const saveSpinner = document.getElementById('saveSpinner');
                
                saveBtn.disabled = true;
                saveText.style.display = 'none';
                saveSpinner.style.display = 'inline-block';
                
                try {
                    const formData = new FormData(e.target);
                    const wineryData = Object.fromEntries(formData);

                    // Add operating hours and timezone
                    wineryData.operational_hours = JSON.stringify(collectOperatingHours());
                    wineryData.timezone = document.getElementById('timezone').value;
                    
                    // Handle logo upload if file selected
                    const logoFile = document.getElementById('wineryLogo').files[0];
                    if (logoFile) {
                        try {
                            wineryData.logo = await this.uploadLogoToBlob(logoFile, wineryData.slug || 'temp');
                        } catch (error) {
                            alert('Logo upload failed. Saving winery without logo.');
                            console.error('Logo upload error:', error);
                        }
                    }

                    // Add operating hours and timezone
                    wineryData.operational_hours = JSON.stringify(collectOperatingHours());
                    wineryData.timezone = document.getElementById('timezone').value;
                    
                    // FIXED: Use query parameter pattern like wines.js/plateaux.js
                    const method = this.isEditing ? 'PATCH' : 'POST';
                    const url = this.isEditing ? 
                        `/api/admin/settings?id=${wineryData.wineryId}` :
                        '/api/admin/settings';
                    
                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        },
                        body: JSON.stringify(wineryData)
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        this.hideForm();
                        await this.loadWineries();
                        
                        // Show success message with subdomain info
                        if (!this.isEditing && result.winery) {
                            alert(`Winery created successfully!\n\nSubdomain: ${result.winery.slug}.ezvino.app\nYou can now access it at https://${result.winery.slug}.ezvino.app`);
                        } else {
                            alert(this.isEditing ? 'Winery updated successfully!' : 'Winery added successfully!');
                        }
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to save winery'}`);
                    }
                } catch (error) {
                    console.error('Error saving winery:', error);
                    alert('Failed to save winery. Please try again.');
                } finally {
                    saveBtn.disabled = false;
                    saveText.style.display = 'inline';
                    saveSpinner.style.display = 'none';
                }
            }
            
            async toggleStatus(wineryId) {
                try {
                    // FIXED: Use query parameter pattern like other APIs
                    const response = await fetch(`/api/admin/settings?action=toggle&id=${wineryId}`, {
                        method: 'PUT',
                        headers: { 
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}` 
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        alert(data.message);
                        await this.loadWineries();
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to update winery status'}`);
                    }
                } catch (error) {
                    console.error('Error toggling status:', error);
                    alert('Failed to update winery status');
                }
            }
            
            async deleteWinery(wineryId) {
                const winery = this.wineries.find(w => w.id === wineryId);
                if (!winery) return;
                
                if (!confirm(`Are you sure you want to delete "${winery.name}"?\n\nThis will make the winery and its subdomain (${winery.slug}.ezvino.app) unavailable.`)) {
                    return;
                }
                
                try {
                    // FIXED: Use query parameter pattern like other APIs
                    const response = await fetch(`/api/admin/settings?id=${wineryId}`, {
                        method: 'DELETE',
                        headers: { 
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}` 
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        alert(data.message);
                        await this.loadWineries();
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to delete winery'}`);
                    }
                } catch (error) {
                    console.error('Error deleting winery:', error);
                    alert('Failed to delete winery');
                }
            }

            async uploadLogoToBlob(file, winerySlug) {
                try {
                    const response = await fetch(`/api/admin/upload-logo?filename=${winerySlug}-logo.png`, {
                        method: 'POST',
                        body: file, // Send file directly like Vercel example
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        return result.url;
                    } else {
                        throw new Error('Upload failed');
                    }
                } catch (error) {
                    console.error('Logo upload error:', error);
                    throw error;
                }
            }

            getWineryInitials(name) {
                return name.split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
            }
        }
        
        const wineryManager = new WineryManager();
    </script>
</body>
</html>