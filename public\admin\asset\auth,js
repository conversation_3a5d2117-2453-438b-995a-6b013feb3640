class GoogleAuth {
    constructor() {
        this.loginBtn = document.getElementById('googleLoginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
        this.init();
    }
    
    init() {
        // Check if already authenticated
        if (this.isAuthenticated()) {
            this.redirectToDashboard();
            return;
        }
        
        // Check for URL parameters (from OAuth callback)
        this.handleCallback();
        
        // Google login button
        this.loginBtn.addEventListener('click', () => this.initiateGoogleLogin());
    }
    
    handleCallback() {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const name = urlParams.get('name');
        const error = urlParams.get('error');
        
        if (error) {
            this.showError(this.getErrorMessage(error));
            window.history.replaceState({}, document.title, window.location.pathname);
            return;
        }
        
        if (token && name) {
            localStorage.setItem('adminAuth', token);
            localStorage.setItem('adminAuthTime', Date.now().toString());
            
            this.showSuccess(`Welcome ${decodeURIComponent(name)}! Redirecting...`);
            
            window.history.replaceState({}, document.title, window.location.pathname);
            setTimeout(() => this.redirectToDashboard(), 1500);
        }
    }
    
    getErrorMessage(error) {
        const messages = {
            'access_denied': 'Access denied. Please try again.',
            'no_code': 'Authentication failed. Please try again.',
            'unauthorized': 'Your Google account is not authorized for admin access.',
            'server_error': 'Server error. Please try again.'
        };
        return messages[error] || 'Authentication failed. Please try again.';
    }
    
    initiateGoogleLogin() {
        this.hideMessages();
        this.setLoading(true);
        window.location.href = '/api/auth/google';
    }
    
    setLoading(loading) {
        this.loginBtn.disabled = loading;
        if (loading) {
            this.loginBtn.innerHTML = '<div class="loading"></div><span>Signing in...</span>';
        } else {
            this.loginBtn.innerHTML = '<div class="google-icon"></div><span>Sign in with Google</span>';
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.successMessage.style.display = 'none';
    }
    
    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
        this.errorMessage.style.display = 'none';
    }
    
    hideMessages() {
        this.errorMessage.style.display = 'none';
        this.successMessage.style.display = 'none';
    }
    
    isAuthenticated() {
        const token = localStorage.getItem('adminAuth');
        const authTime = localStorage.getItem('adminAuthTime');
        
        if (!token || !authTime) {
            return false;
        }
        
        const oneHour = 60 * 60 * 1000;
        const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
        
        if (isExpired) {
            this.clearAuth();
            return false;
        }
        
        return true;
    }
    
    clearAuth() {
        localStorage.removeItem('adminAuth');
        localStorage.removeItem('adminAuthTime');
    }
    
    redirectToDashboard() {
        window.location.href = '/admin/index.html';
    }
}

// Initialize when DOM loads
document.addEventListener('DOMContentLoaded', () => {
    new GoogleAuth();
});