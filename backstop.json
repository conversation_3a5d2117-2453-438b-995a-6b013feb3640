{"id": "wine_tasting_visual_regression", "viewports": [{"label": "mobile", "width": 375, "height": 667}, {"label": "tablet", "width": 768, "height": 1024}, {"label": "desktop", "width": 1920, "height": 1080}], "onBeforeScript": "puppet/onBefore.cjs", "onReadyScript": "puppet/onReady.cjs", "scenarios": [{"label": "Guest Index - French - Main Content", "url": "http://ezvino-demo.localhost:3000/guest/index.html", "readySelector": "#mainContent", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setFrench.cjs", "onReadyScript": "puppet/waitForContent.cjs"}, {"label": "Guest Index - English - Main Content", "url": "http://ezvino-demo.localhost:3000/guest/index.html", "readySelector": "#mainContent", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setEnglish.cjs", "onReadyScript": "puppet/waitForContent.cjs"}, {"label": "Guest Index - Premium Winery Logo", "url": "http://ezvino-demo.localhost:3000/guest/index.html", "readySelector": "#wineryLogo", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setFrench.cjs", "onReadyScript": "puppet/checkPremiumLogo.cjs"}, {"label": "Guest Session - French - Active", "url": "http://ezvino-demo.localhost:3000/guest/session.html?code=TEST123", "readySelector": "#mainContent", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setFrench.cjs"}, {"label": "Guest Session - English - Active", "url": "http://ezvino-demo.localhost:3000/guest/session.html?code=TEST123", "readySelector": "#mainContent", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setEnglish.cjs"}, {"label": "Host Dashboard - French", "url": "http://ezvino-demo.localhost:3000/host/dashboard.html", "readySelector": "#sessionsGrid", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setFrench.cjs"}, {"label": "Host Dashboard - English", "url": "http://ezvino-demo.localhost:3000/host/dashboard.html", "readySelector": "#sessionsGrid", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setEnglish.cjs"}, {"label": "Host Session - French - Wine Toggles", "url": "http://ezvino-demo.localhost:3000/host/session.html?id=1", "readySelector": "#wineOrders", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setFrench.cjs"}, {"label": "Host Session - English - Wine Toggles", "url": "http://ezvino-demo.localhost:3000/host/session.html?id=1", "readySelector": "#wineOrders", "delay": 2000, "misMatchThreshold": 0.1, "onBeforeScript": "puppet/setEnglish.cjs"}], "paths": {"bitmaps_reference": "backstop_data/bitmaps_reference", "bitmaps_test": "backstop_data/bitmaps_test", "engine_scripts": "backstop_data/engine_scripts", "html_report": "backstop_data/html_report", "ci_report": "backstop_data/ci_report"}, "report": ["browser"], "engine": "puppeteer", "engineOptions": {"args": ["--no-sandbox"]}, "asyncCaptureLimit": 5, "asyncCompareLimit": 50, "debug": false, "debugWindow": false}