import { query } from '../lib/database.js';

export default async function handler(req, res) {
  // Security: Only allow in development or with specific header
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🍷 Creating wine_categories table...');
    
    // Create table
    await query(`
      CREATE TABLE IF NOT EXISTS wine_categories (
        id SERIAL PRIMARY KEY,
        category_code VARCHAR(50) UNIQUE NOT NULL,
        name_fr VARCHAR(100) NOT NULL,
        name_en VARCHAR(100) NOT NULL,
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Insert categories
    const categories = [
      ['red', 'Vin Rouge', 'Red Wine', 1],
      ['white', 'Vin Blanc', 'White Wine', 2],
      ['rose', 'Vin Rosé', 'Rosé', 3],
      ['sparkling', '<PERSON> Mo<PERSON>', 'Sparkling', 4],
      ['dessert', 'Vin de Dessert', 'Dessert Wine', 5],
      ['cider', 'Cidre', 'Cider', 6],
      ['orange', 'Vin Orange', 'Orange Wine', 7],
      ['natural_red', 'Vin Nature Rouge', 'Natural Red', 8],
      ['natural_white', 'Vin Nature Blanc', 'Natural White', 9],
      ['natural_rose', 'Vin Nature Rosé', 'Natural Rosé', 10],
      ['fortified', 'Vin Fortifié', 'Fortified Wine', 11],
      ['ice_wine', 'Vin de Glace', 'Ice Wine', 12],
      ['pet_nat', 'Pétillant Naturel', 'Pet-Nat', 13]
    ];
    
    let inserted = 0;
    for (const [code, name_fr, name_en, sort_order] of categories) {
      await query(`
        INSERT INTO wine_categories (category_code, name_fr, name_en, sort_order)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (category_code) DO NOTHING
      `, [code, name_fr, name_en, sort_order]);
      inserted++;
    }
    
    // Verify
    const result = await query('SELECT COUNT(*) as count FROM wine_categories');
    
    res.status(200).json({
      success: true,
      message: 'Categories table created and populated',
      totalCategories: result.rows[0].count,
      processed: inserted
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
}