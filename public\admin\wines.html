<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wine Management - Admin</title>
    <link rel="stylesheet" href="../shared/styles.css">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .admin-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #722f37;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #722f37;
            text-decoration: none;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #722f37;
            margin-bottom: 0.3rem;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #722f37;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a252a;
        }
        
        .btn-secondary {
            background: #e9ecef;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #dee2e6;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .wine-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .wine-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .wine-card:hover {
            transform: translateY(-5px);
        }
        
        .wine-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .wine-image {
            width: 60px;
            height: 80px;
            background: linear-gradient(135deg, #722f37, #8b3a42);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .wine-info {
            flex: 1;
            margin-left: 1rem;
        }
        
        .wine-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.3rem;
        }
        
        .wine-category {
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-bottom: 0.5rem;
            display: inline-block;
        }
        
        .category-red { background: #ffebee; color: #c62828; }
        .category-white { background: #fff8e1; color: #f57f17; }
        .category-rose { background: #fce4ec; color: #ad1457; }
        .category-sparkling { background: #e3f2fd; color: #1565c0; }
        .category-dessert { background: #f3e5f5; color: #7b1fa2; }
        .category-port { background: #e8f5e8; color: #2e7d32; }
        .category-cider { background: #fff3e0; color: #ef6c00; }
        .category-orange { background: #fff3e0; color: #ef6c00; }
        .category-natural { background: #e8f5e8; color: #2e7d32; }
        .category-fortified { background: #f3e5f5; color: #7b1fa2; }
        
        .wine-details {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }
        
        .wine-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .wine-price {
            font-weight: 600;
            color: #722f37;
        }
        
        .wine-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-toggle {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .status-toggle.active {
            background: #28a745;
        }
        
        .status-toggle::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
        }
        
        .status-toggle.active::after {
            transform: translateX(20px);
        }
        
        .wine-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .edit-btn { background: #17a2b8; color: white; }
        .edit-btn:hover { background: #138496; }
        .delete-btn { background: #dc3545; color: white; }
        .delete-btn:hover { background: #c82333; }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .wine-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: none;
        }
        
        .wine-form.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #722f37;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-select {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            cursor: pointer;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #722f37;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="admin-logo">
            <span>🍷</span>
            Wine Management
        </div>
        <div class="breadcrumb">
            <a href="/admin/index.html">Dashboard</a>
            <span>→</span>
            <span id="currentWineryName">Wine Management</span>
        </div>
    </div>
    
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <div class="page-title" id="pageTitle">Wine Management</div>
                <div class="page-subtitle" id="pageSubtitle">Manage wine selection for winery</div>
            </div>
            <div style="display: flex; gap: 1rem;">
                <button class="btn btn-secondary" onclick="window.location.href='/admin/index.html'">
                    ← Back to Dashboard
                </button>
                <button class="btn btn-primary" onclick="wineManager.showAddForm()">
                    + Add Wine
                </button>
            </div>
        </div>
        
        <!-- Wine Add/Edit Form -->
        <div class="wine-form" id="wineForm">
            <h3 id="formTitle">Add New Wine</h3>
            
            <form id="wineFormElement">
                <input type="hidden" id="wineId" name="wineId">
                <input type="hidden" id="wineryId" name="wineryId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="wineName" class="form-label">Wine Name *</label>
                        <input type="text" id="wineName" name="name" class="form-input" required placeholder="e.g. Château Rouge Reserve">
                    </div>
                    
                    <div class="form-group">
                        <label for="wineCategory" class="form-label">Category *</label>
                        <select id="wineCategory" name="category" class="form-select" required>
                            <option value="">Loading categories...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="wineVintage" class="form-label">Vintage</label>
                        <input type="number" id="wineVintage" name="vintage" class="form-input" min="1900" max="2030" placeholder="2020">
                    </div>
                    
                    <div class="form-group">
                        <label for="wineAlcohol" class="form-label">Alcohol Content (%)</label>
                        <input type="number" id="wineAlcohol" name="alcohol_content" class="form-input" min="0" max="50" step="0.1" placeholder="13.5">
                    </div>
                    
                    <div class="form-group">
                        <label for="winePrice" class="form-label">Price ($)</label>
                        <input type="number" id="winePrice" name="price" class="form-input" min="0" step="0.01" placeholder="25.99">
                    </div>
                    
                    <div class="form-group">
                        <label for="wineSort" class="form-label">Sort Order</label>
                        <input type="number" id="wineSort" name="sort_order" class="form-input" min="0" placeholder="0">
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="wineDescriptionFr" class="form-label">Description (French)</label>
                        <textarea id="wineDescriptionFr" name="description_fr" class="form-input form-textarea" placeholder="Description en français..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="wineDescriptionEn" class="form-label">Description (English)</label>
                        <textarea id="wineDescriptionEn" name="description_en" class="form-input form-textarea" placeholder="English description..."></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="wineManager.hideForm()">Cancel</button>
                    <button type="submit" class="btn btn-success" id="saveBtn">
                        <span id="saveText">Save Wine</span>
                        <span id="saveSpinner" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Wine Grid -->
        <div id="wineGrid" class="wine-grid">
            <div class="loading" style="grid-column: 1 / -1; text-align: center; padding: 2rem;"></div>
        </div>
    </div>

    <script>
        // Wine Management Logic
        class WineManager {
            constructor() {
                this.currentWinery = null;
                this.wines = [];
                this.isEditing = false;
                this.init();
            }
            
            async init() {
                // Check authentication
                if (!this.isAuthenticated()) {
                    window.location.href = '/admin/auth.html';
                    return;
                }
                
                // Get winery from URL
                const urlParams = new URLSearchParams(window.location.search);
                const wineryId = urlParams.get('winery');
                
                if (!wineryId) {
                    alert('No winery specified');
                    window.location.href = '/admin/index.html';
                    return;
                }
                
                // Load winery and wines
                await this.loadWinery(wineryId);
                await this.loadWines();
                await this.loadCategories();
                
                // Setup form submission
                document.getElementById('wineFormElement').addEventListener('submit', (e) => this.handleSubmit(e));
            }
            
            isAuthenticated() {
                const token = localStorage.getItem('adminAuth');
                const authTime = localStorage.getItem('adminAuthTime');
                
                if (!token || !authTime) {
                    return false;
                }
                
                const oneHour = 60 * 60 * 1000;
                const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
                
                return !isExpired;
            }
            
            async loadWinery(wineryId) {
                try {
                    // LOAD ACTUAL WINERY FROM DATABASE
                    const response = await fetch('/api/admin/settings', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.currentWinery = data.wineries.find(w => w.id == wineryId);
                    }
                    
                    if (this.currentWinery) {
                        document.getElementById('pageTitle').textContent = `Wine Management - ${this.currentWinery.name}`;
                        document.getElementById('pageSubtitle').textContent = `Manage wine selection for ${this.currentWinery.name}`;
                        document.getElementById('currentWineryName').textContent = this.currentWinery.name;
                        document.getElementById('wineryId').value = this.currentWinery.id;
                    } else {
                        alert('Winery not found');
                        window.location.href = '/admin/index.html';
                    }
                } catch (error) {
                    console.error('Error loading winery:', error);
                    alert('Failed to load winery');
                    window.location.href = '/admin/index.html';
                }
            }
            
            async loadWines() {
                try {
                    const response = await fetch(`/api/admin/wines?winery=${this.currentWinery.id}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.wines = data.wines || [];
                    } else {
                        console.error('Failed to load wines:', response.status);
                        this.wines = [];
                    }
                    
                    this.renderWines();
                } catch (error) {
                    console.error('Error loading wines:', error);
                    this.wines = [];
                    this.renderWines();
                }
            }
            
            renderWines() {
                const grid = document.getElementById('wineGrid');
                
                if (this.wines.length === 0) {
                    grid.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🍷</div>
                            <h3>No wines added yet</h3>
                            <p>Start building your wine selection by adding your first wine.</p>
                            <button class="btn btn-primary" onclick="wineManager.showAddForm()">Add First Wine</button>
                        </div>
                    `;
                    return;
                }
                
                grid.innerHTML = this.wines.map(wine => `
                    <div class="wine-card">
                        <div class="wine-actions">
                            <button class="action-btn edit-btn" onclick="wineManager.editWine(${wine.id})">✏️</button>
                            <button class="action-btn delete-btn" onclick="wineManager.softDeleteWine(${wine.id})">🗑️</button>
                            <button class="action-btn hard-delete-btn" onclick="wineManager.hardDeleteWine(${wine.id})" title="Permanent Delete">💥</button>
                        </div>
                        
                        <div class="wine-header">
                            <div class="wine-image">🍷</div>
                            <div class="wine-info">
                                <div class="wine-name">${wine.name}</div>
                                <div class="wine-category category-${wine.category}">${this.getCategoryLabel(wine.category)}</div>
                                <div class="wine-details">
                                    ${wine.vintage ? `${wine.vintage} • ` : ''}
                                    ${wine.alcohol_content ? `${wine.alcohol_content}% ABV` : ''}
                                </div>
                            </div>
                        </div>
                        
                        <div class="wine-meta">
                            <div class="wine-price">$${wine.price || '0.00'}</div>
                            <div class="wine-status">
                                <span>${wine.is_available ? 'Available' : 'Unavailable'}</span>
                                <div class="status-toggle ${wine.is_available ? 'active' : ''}" 
                                     onclick="wineManager.toggleAvailability(${wine.id})"></div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            getCategoryLabel(category) {
                const labels = {
                    red: 'Red Wine',
                    white: 'White Wine',
                    rose: 'Rosé',
                    sparkling: 'Sparkling',
                    dessert: 'Dessert',
                    port: 'Port',
                    cider: 'Cider'
                };
                return labels[category] || category;
            }
            
            showAddForm() {
                this.isEditing = false;
                document.getElementById('formTitle').textContent = 'Add New Wine';
                document.getElementById('saveText').textContent = 'Save Wine';
                document.getElementById('wineFormElement').reset();
                document.getElementById('wineId').value = '';
                document.getElementById('wineryId').value = this.currentWinery.id;
                document.getElementById('wineForm').classList.add('active');
                document.getElementById('wineName').focus();
            }
            
            hideForm() {
                document.getElementById('wineForm').classList.remove('active');
            }
            
            editWine(wineId) {
                const wine = this.wines.find(w => w.id === wineId);
                if (!wine) return;
                
                this.isEditing = true;
                document.getElementById('formTitle').textContent = 'Edit Wine';
                document.getElementById('saveText').textContent = 'Update Wine';
                
                // Populate form
                document.getElementById('wineId').value = wine.id;
                document.getElementById('wineryId').value = this.currentWinery.id;
                document.getElementById('wineName').value = wine.name;
                document.getElementById('wineCategory').value = wine.category;
                document.getElementById('wineVintage').value = wine.vintage || '';
                document.getElementById('wineAlcohol').value = wine.alcohol_content || '';
                document.getElementById('winePrice').value = wine.price || '';
                document.getElementById('wineSort').value = wine.sort_order || '';
                document.getElementById('wineDescriptionFr').value = wine.description_fr || '';
                document.getElementById('wineDescriptionEn').value = wine.description_en || '';
                
                document.getElementById('wineForm').classList.add('active');
                document.getElementById('wineName').focus();
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const saveBtn = document.getElementById('saveBtn');
                const saveText = document.getElementById('saveText');
                const saveSpinner = document.getElementById('saveSpinner');
                
                saveBtn.disabled = true;
                saveText.style.display = 'none';
                saveSpinner.style.display = 'inline-block';
                
                try {
                    const formData = new FormData(e.target);
                    const wineData = Object.fromEntries(formData);
                    
                    const method = this.isEditing ? 'PATCH' : 'POST';
                    const url = this.isEditing ? 
                        `/api/admin/wines?id=${wineData.wineId}` : 
                        '/api/admin/wines';
                    
                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        },
                        body: JSON.stringify(wineData)
                    });
                    
                    if (response.ok) {
                        this.hideForm();
                        await this.loadWines();
                        alert(this.isEditing ? 'Wine updated successfully!' : 'Wine added successfully!');
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error || 'Failed to save wine'}`);
                    }
                } catch (error) {
                    console.error('Error saving wine:', error);
                    alert('Failed to save wine. Please try again.');
                } finally {
                    saveBtn.disabled = false;
                    saveText.style.display = 'inline';
                    saveSpinner.style.display = 'none';
                }
            }
            
            async toggleAvailability(wineId) {
                try {
                    const response = await fetch(`/api/admin/wines?action=toggle&id=${wineId}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('adminAuth')}`
                        }
                    });
                    
                    if (response.ok) {
                        await this.loadWines();
                    } else {
                        alert('Failed to update wine availability');
                    }
                } catch (error) {
                    console.error('Error toggling availability:', error);
                    alert('Failed to update wine availability');
                }
            }
            
            async hardDeleteWine(wineId) {
                const wine = this.wines.find(w => w.id === wineId);
                if (!wine) return;
                
                // MULTI-STEP CONFIRMATION (your brilliant pattern)
                if (!confirm(`⚠️ PERMANENT DELETION WARNING ⚠️\n\nWine: "${wine.name}"\n\nThis will PERMANENTLY remove the wine from the database.\nThis action CANNOT be undone.\n\nProceed?`)) {
                    return;
                }
                
                const confirmText = prompt('Type "DELETE FOREVER" to confirm:');
                if (confirmText !== 'DELETE FOREVER') {
                    alert('Deletion cancelled');
                    return;
                }
                
                try {
                    const response = await fetch(`/api/admin/wines?id=${wineId}&hard=true`, {
                        method: 'DELETE',
                        headers: { 'Authorization': `Bearer ${localStorage.getItem('adminAuth')}` }
                    });
                    
                    if (response.ok) {
                        alert('Wine permanently deleted');
                        await this.loadWines();
                    } else {
                        const error = await response.json();
                        alert(`Error: ${error.error}`);
                    }
                } catch (error) {
                    alert('Failed to delete wine permanently');
                }
            }
        
            async loadCategories() {
                try {
                    const response = await fetch(`/api/admin/wines?action=categories`, {
                        headers: { 'Authorization': `Bearer ${localStorage.getItem('adminAuth')}` }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const dropdown = document.getElementById('wineCategory');
                        dropdown.innerHTML = '<option value="">Select category</option>';
                        
                        data.categories.forEach(cat => {
                            const option = document.createElement('option');
                            option.value = cat.category_code;
                            option.textContent = `${cat.name_fr} / ${cat.name_en}`;
                            dropdown.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('Failed to load categories');
                }
                
            }
        }
        
        // Initialize wine manager
        const wineManager = new WineManager();
    </script>
</body>
</html>
