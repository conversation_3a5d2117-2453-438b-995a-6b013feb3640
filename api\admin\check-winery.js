// api/admin/check-winery.js
// Check current winery settings and constraints

import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get all wineries with their guest limits
    const wineries = await query(`
      SELECT id, name, slug, winery_code,
             min_guests_per_group, max_guests_per_group,
             wines_per_guest, max_plateaux_per_group,
             subscription_tier, is_active,
             created_at
      FROM wineries 
      ORDER BY id;
    `);

    // Get sample session to test limits
    const recentSession = await query(`
      SELECT ts.group_code, ts.guest_count, w.name as winery_name
      FROM tasting_sessions ts
      JOIN wineries w ON ts.winery_id = w.id
      ORDER BY ts.created_at DESC
      LIMIT 1;
    `);

    res.status(200).json({ 
      success: true, 
      wineries: wineries.rows,
      recent_session: recentSession.rows[0] || null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Check winery error:', error);
    res.status(500).json({ 
      error: 'Failed to check winery status: ' + error.message 
    });
  }
}