/* =============================================
 * COMPLETE THEME SYSTEM - FULL CONTROL
 * All visual elements centralized
 * ============================================= */

:root {
  /* Brand Colors */
  --brand-primary: #8b0000;
  --brand-secondary: #A52A2A;
  --wine-gold: #d4af37;
  --wine-cream: #f8f9fa;
  --wine-dark: #8b0000;
  --success-green: #D4EDDA;

  /* Text Colors - Fixed Readability */
  --text-french: #000000;
  --text-english: #000000;  /* Darker - readable on burgundy */
  --text-light: #666666;
  --text-white: #ffffff;
}

/* French/English Text Classes */
.french-text { color: var(--text-french); }
.english-text { color: var(--text-english); }
.french-text-white { color: var(--wine-cream); }
.english-text-white { color: var(--wine-cream); }

/* public/admin/adminstyle.css - Admin specific styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
}

.closed-messages-section {
  margin-top: 2rem;
}

.section-title {
  color: #722f37;
  margin-bottom: 1rem;
}

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000;
  padding: 2rem;
}

.auth-card {
  background: #000000;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.auth-logo {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.auth-logo img {
  max-height: 60px;
  max-width: 200px;
  object-fit: contain;
}

.auth-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--brand-primary);
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  color: var(--text-light);
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

/* Google OAuth Button */
.google-login-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 0.6rem 1rem;
  font-size: 14px;
  font-weight: 500;
  color: #3c4043;
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.google-login-btn:hover {
  background: #f7f8f8;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.google-icon {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

.google-login-btn svg {
  width: 18px;
  height: 18px;
}

.google-text {
  flex: 1;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.8rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  display: none;
}

.success-message {
  background: var(--success-green);
  color: #155724;
  padding: 0.8rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  display: none;
}

.auth-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  font-size: 0.8rem;
  color: var(--text-light);
}

.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--brand-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
