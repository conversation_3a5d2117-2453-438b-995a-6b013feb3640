import { query } from '../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'DELETE') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    const { sessionId } = req.query;
    
    if (!sessionId) {
        return res.status(400).json({ error: 'Session ID required' });
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' });
    }

    try {
        // Delete in proper order due to foreign key constraints
        await query('DELETE FROM wine_orders WHERE session_id = $1', [sessionId]);
        await query('DELETE FROM plateau_orders WHERE session_id = $1', [sessionId]);
        await query('DELETE FROM guests WHERE session_id = $1', [sessionId]);
        await query('DELETE FROM session_locks WHERE session_id = $1', [sessionId]);
        const result = await query('DELETE FROM tasting_sessions WHERE id = $1', [sessionId]);
        
        if (result.rowCount === 0) {
            return res.status(404).json({ error: 'Session not found' });
        }
        
        res.status(200).json({ 
            success: true, 
            message: `Session ${sessionId} deleted successfully` 
        });
        
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({ error: 'Failed to delete session', details: error.message });
    }
}