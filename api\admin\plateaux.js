// api/admin/plateaux.js
// CLEAN VERSION - Fixed structure and duplicate sections

import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  try {
    // Simple auth check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    // GET - List plateaux for a winery
    if (req.method === 'GET') {
      const { winery } = req.query;
      
      if (!winery) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID is required'
        });
      }

      const queryText = `
        SELECT * FROM plateaux 
        WHERE winery_id = $1 
        ORDER BY sort_order ASC, name_fr ASC
      `;

      const result = await query(queryText, [winery]);

      return res.status(200).json({
        success: true,
        plateaux: result.rows
      });
    }

    // POST - Create new plateau
    if (req.method === 'POST') {
      const {
        wineryId,
        name_fr,
        name_en,
        description_fr,
        description_en,
        price,
        sort_order
      } = req.body;

      // Validation
      if (!wineryId || !name_fr || !name_en) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID, French name, and English name are required'
        });
      }

      // Verify winery exists
      const wineryCheck = await query(
        'SELECT id FROM wineries WHERE id = $1 AND is_active = true',
        [wineryId]
      );

      if (!wineryCheck.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Winery not found'
        });
      }

      const insertQuery = `
        INSERT INTO plateaux (
          winery_id, name_fr, name_en, description_fr, description_en,
          price, sort_order, is_available
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, true)
        RETURNING *
      `;

      const values = [
        wineryId,
        name_fr.trim(),
        name_en.trim(),
        description_fr?.trim() || null,
        description_en?.trim() || null,
        price ? parseFloat(price) : null,
        sort_order ? parseInt(sort_order) : 0
      ];

      const result = await query(insertQuery, values);

      return res.status(201).json({
        success: true,
        plateau: result.rows[0],
        message: 'Plateau created successfully'
      });
    }

    // PATCH - Update existing plateau
    if (req.method === 'PATCH') {
      const { id: plateauId } = req.query;
      
      if (!plateauId) {
        return res.status(400).json({
          success: false,
          error: 'Plateau ID is required'
        });
      }

      const {
        name_fr,
        name_en,
        description_fr,
        description_en,
        price,
        sort_order,
        is_available
      } = req.body;

      // Build dynamic update query
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      if (name_fr !== undefined) {
        updateFields.push(`name_fr = $${paramIndex++}`);
        updateValues.push(name_fr.trim());
      }
      if (name_en !== undefined) {
        updateFields.push(`name_en = $${paramIndex++}`);
        updateValues.push(name_en.trim());
      }
      if (description_fr !== undefined) {
        updateFields.push(`description_fr = $${paramIndex++}`);
        updateValues.push(description_fr?.trim() || null);
      }
      if (description_en !== undefined) {
        updateFields.push(`description_en = $${paramIndex++}`);
        updateValues.push(description_en?.trim() || null);
      }
      if (price !== undefined) {
        updateFields.push(`price = $${paramIndex++}`);
        updateValues.push(price ? parseFloat(price) : null);
      }
      if (sort_order !== undefined) {
        updateFields.push(`sort_order = $${paramIndex++}`);
        updateValues.push(sort_order ? parseInt(sort_order) : 0);
      }
      if (is_available !== undefined) {
        updateFields.push(`is_available = $${paramIndex++}`);
        updateValues.push(Boolean(is_available));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No fields to update'
        });
      }

      updateValues.push(plateauId);

      const updateQuery = `
        UPDATE plateaux 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;

      const result = await query(updateQuery, updateValues);

      if (!result.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Plateau not found'
        });
      }

      return res.status(200).json({
        success: true,
        plateau: result.rows[0],
        message: 'Plateau updated successfully'
      });
    }

    // DELETE - Remove plateau (soft delete)
    if (req.method === 'DELETE') {
      const { id: plateauId } = req.query;
      
      if (!plateauId) {
        return res.status(400).json({
          success: false,
          error: 'Plateau ID is required'
        });
      }

      const deleteQuery = `
        UPDATE plateaux 
        SET is_available = false
        WHERE id = $1
        RETURNING *
      `;

      const result = await query(deleteQuery, [plateauId]);

      if (!result.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Plateau not found'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Plateau deleted successfully'
      });
    }

    // PUT - Toggle plateau availability
    if (req.method === 'PUT') {
      const { action, id } = req.query;
      
      if (action === 'toggle' && id) {
        const toggleQuery = `
          UPDATE plateaux 
          SET is_available = NOT is_available
          WHERE id = $1
          RETURNING *
        `;

        const result = await query(toggleQuery, [id]);

        if (!result.rows.length) {
          return res.status(404).json({
            success: false,
            error: 'Plateau not found'
          });
        }

        return res.status(200).json({
          success: true,
          plateau: result.rows[0],
          message: `Plateau ${result.rows[0].is_available ? 'enabled' : 'disabled'}`
        });
      }
    }

    // Method not allowed
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });

  } catch (error) {
    console.error('Plateau management error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}