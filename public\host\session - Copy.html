<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Session Management - Wine Tasting</title>
    <link rel="stylesheet" href="../shared/theme.css">
    <script src="../shared/premiumFeatures.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--wine-cream) 0%, #f8f6f0 100%);
            color: var(--wine-cream);
            line-height: 1.6;
            min-height: 100vh;
            padding: 0;
            visibility: hidden; /* FOUC Prevention */
        }

        body.loaded {
            visibility: visible; /* Show after CSS loads */
        }

        .app-container {
            max-width: 800px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        /* Header - Unified Architecture */
        .header {
            background: var(--brand-primary);
            color: white;
            padding: 20px 30px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="header-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23header-pattern)"/></svg>') repeat;
            opacity: 0.3;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* Row 1: Subtitle + Language Switcher */
        .header-row-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .winery-subtitle {
            font-size: 14px;
            color: rgba(255,255,255,0.9);
        }

        /* Row 2: Logo + Winery Name */
        .header-row-2 {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .winery-logo {
            width: 50px;
            height: 50px;
            background: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: transparent !important;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .winery-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .header-row-2 h1 {
            color: white;
            font-size: 28px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin: 0;
        }

        /* Row 3: Back button */
        .header-row-3 {
            display: flex;
            justify-content: center;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
                text-align: left !important;
            }

            .header-content {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            }

            /* Language switcher override */
            .language-switcher {
                position: static;
            }

            .header-row-1 {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .winery-subtitle {
                font-size: 12px;
            }

            .header-row-2 {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }

            .winery-logo {
                width: 35px;
                height: 35px;
                margin: 0;
            }

            .header-row-2 h1 {
                font-size: 20px;
                line-height: 1.3;
                margin: 0;
            }

            .header-row-3 {
                width: 100%;
                margin-bottom: 10px;
            }

            .app-container {
                max-width: 100%;
            }
            
            .main-content {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header - Host Version -->
        <header class="header">
            <div class="header-content">
                <!-- Row 1: Subtitle + Language Switcher (will be added by JS) -->
                <div class="header-row-1">
                    <div class="winery-subtitle">
                        <span class="french-text-white">Gestion de sessions</span>
                        <span class="english-text-white">Session Management</span>
                    </div>
                </div>
                
                <!-- Row 2: Logo + Winery Name -->
                <div class="header-row-2">
                    <div class="winery-logo" id="sessionWineryLogo">CT</div>
                    <h1 id="sessionWineryName">Loading...</h1>
                </div>
                
                <!-- Row 3: Back button -->
                <div class="header-row-3">
                    <button class="back-btn">
                        ← <span class="french-text">Tableau des hôtes</span>
                        <span class="english-text">Hosts dashboard</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loadingState" class="loading">
            <div class="loading-spinner"></div>
            <p>
                <span class="french-text">Chargement de la session...</span>
                <span class="english-text">Loading session...</span>
            </p>
        </div>

        <!-- Main Content -->
        <main class="main-content host-session" id="mainContent" style="display: none;">
            
            <!-- Session Information -->
            <section class="section">
                <div class="section-content">
                    <div class="session-card" id="sessionCard">
                        <!-- Session info will be loaded dynamically -->
                    </div>
                </div>
            </section>

            <!-- Wine Orders -->
            <section class="section">
                <div class="section-header">
                    <span>
                        <span class="french-text">Sélection par invité</span>
                        <span class="english-text">Selection by guest</span>
                    </span>
                </div>
                <div class="section-content" id="wineOrders">
                    <!-- Wine orders with host controls will be loaded dynamically -->
                </div>
            </section>

            <!-- Plateau Orders -->
            <section class="section" id="plateauSection" style="display: none;">
                <div class="section-header">
                    <span>
                        <span class="french-text">Plateau</span>
                        <span class="english-text">Board</span>
                    </span>
                </div>
                <div class="section-content" id="plateauOrders">
                    <!-- Plateau orders with host controls will be loaded dynamically -->
                </div>
            </section>

        </main>
    </div>

    <script>
        // =============================================
        // Language switching - Complete Version
        // =============================================

        function createLanguageSwitcher() {
            const switchButton = document.createElement('div');
            switchButton.className = 'language-switcher';
            switchButton.innerHTML = `
                <button class="lang-btn" id="fr-btn">FR</button>
                <button class="lang-btn" id="en-btn">EN</button>
            `;
            
            // Append to header instead of body
            const headerRow1 = document.querySelector('.header-row-1');
            if (headerRow1) {
                headerRow1.appendChild(switchButton);
            } else {
                document.body.appendChild(switchButton);
            }
            
            // Add click handlers with localStorage
            document.getElementById('fr-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'fr');
                document.body.setAttribute('data-lang', 'fr');
                document.getElementById('fr-btn').classList.add('active');
                document.getElementById('en-btn').classList.remove('active');
                console.log('French mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };

            document.getElementById('en-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'en');
                document.body.setAttribute('data-lang', 'en');
                document.getElementById('en-btn').classList.add('active');
                document.getElementById('fr-btn').classList.remove('active');
                console.log('English mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };
            
            // Load saved preference or default to French
            const savedLang = localStorage.getItem('selectedLanguage') || 'fr';
            document.getElementById(savedLang + '-btn').click();
        }

        // Run when page loads
        document.addEventListener('DOMContentLoaded', createLanguageSwitcher);

        // =============================================
        // HOST SESSION MANAGEMENT - COMPLETE VERSION
        // =============================================

        class HostSessionManagement {
            constructor() {
                this.API_BASE = '/api';
                this.sessionId = this.getSessionIdFromUrl();
                this.sessionData = null;
                this.refreshInterval = null;
                
                // Store handlers for cleanup
                this.globalDragHandler = null;
                this.globalEndDragHandler = null;
                
                this.init();
            }

            getSessionIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            async init() {
                if (!this.sessionId) {
                    this.showError('No session ID provided');
                    return;
                }

                // Set back button link
                document.querySelector('.back-btn').addEventListener('click', () => {
                    window.location.href = 'dashboard.html';
                });

                await this.loadSessionData();
                this.startAutoRefresh();
            }

            async loadSessionData() {
                try {
                    const response = await fetch(`${this.API_BASE}/host/session/id?id=${this.sessionId}`);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: Session not found`);
                    }

                    const data = await response.json();
                    
                    if (!data.success) {
                        throw new Error(data.error || 'Failed to load session');
                    }

                    this.sessionData = data;
                    this.renderSessionData();
                    this.showMainContent();

                } catch (error) {
                    console.error('Error loading session:', error);
                    this.showError(error.message);
                }
            }

            renderSessionData() {
                if (!this.sessionData) return;

                this.renderWineryHeader();
                this.renderSessionInfo();
                this.renderWineOrders();
                this.renderPlateauOrders();
            }

            renderSessionInfo() {
                const sessionCard = document.getElementById('sessionCard');
                const session = this.sessionData.session;
                
                const createdTime = new Date(session.createdAt).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                sessionCard.innerHTML = `
                    <div class="session-group-name">${session.groupName}</div>
                    <div class="session-details">
                        <span class="french-text">Groupe de ${session.guestCount}</span>
                        <span class="english-text">Party of ${session.guestCount}</span>
                    </div>
                    <div class="session-time">
                        <span class="french-text">Créé à </span>
                        <span class="english-text">Created at</span> ${createdTime}
                    </div>
                `;
            }

            renderWineOrders() {
                const wineOrders = document.getElementById('wineOrders');
                wineOrders.innerHTML = '';

                this.sessionData.guests.forEach(guest => {
                    if (guest.wineOrders && guest.wineOrders.length > 0) {
                        const guestCard = document.createElement('div');
                        guestCard.className = 'guest-wine-card';
                        
                        const guestServedCount = guest.wineOrders.filter(wine => wine.status === 'served').length;

                        // KEEP API ORDER - Don't re-sort (already sorted by w.sort_order)
                        const winesInApiOrder = guest.wineOrders;

                        guestCard.innerHTML = `
                            <div class="guest-wine-header">
                                <div class="guest-number">${guest.guestNumber}</div>
                                <div class="guest-wine-info">
                                    <div class="guest-name french-text">${this.getGuestDisplayName(guest.guestName, 'fr', guest.guestNumber)}</div>
                                    <div class="guest-name english-text">${this.getGuestDisplayName(guest.guestName, 'en', guest.guestNumber)}</div>
                                    <div class="guest-wine-count">
                                        ${guestServedCount}/${guest.wineOrders.length} 
                                        <span class="french-text">vins servis</span>
                                        <span class="english-text">wines served</span>
                                    </div>
                                </div>
                            </div>
                            <div class="guest-wines-list">
                                ${winesInApiOrder.map(wine => this.createWineOrderItem(wine)).join('')}
                            </div>
                        `;

                        wineOrders.appendChild(guestCard);
                    }
                });

                // Add event listeners for wine toggles
                // Enhanced drag-to-toggle functionality for wine toggles
                document.querySelectorAll('.wine-toggle').forEach(toggle => {
                    this.addDragToggleBehavior(toggle, (orderId, currentStatus) => {
                        this.toggleWineStatus(orderId, currentStatus);
                    });
                });
            }

            createWineOrderItem(wine) {
                const isServed = wine.status === 'served';
                const toggleClass = isServed ? 'served' : '';
                
                // BILINGUAL STATUS LIKE GUEST SIDE
                const toggleLabel = isServed 
                    ? '<span class="french-text">Servi</span><span class="english-text">Served</span>'
                    : '<span class="french-text">Commandé</span><span class="english-text">Ordered</span>';

                return `
                    <div class="wine-order-item">
                        <div class="wine-item-details">
                            <div class="wine-item-name">${wine.wineName}</div>
                            <div class="wine-item-category">
                                <span class="french-text">${wine.category_name_fr || wine.wineCategory}</span>
                                <span class="english-text">${wine.category_name_en || wine.wineCategory}</span>
                            </div>
                        </div>
                        <div class="serving-toggle wine-toggle ${toggleClass}" 
                            data-order-id="${wine.orderId}" 
                            data-status="${wine.status}">
                            <div class="toggle-slider">${isServed ? '✓' : '○'}</div>
                            <span class="toggle-label">${toggleLabel}</span>
                        </div>
                    </div>
                `;
            }

            renderPlateauOrders() {
                const plateauContainer = document.getElementById('plateauOrders');
                const plateauSection = document.getElementById('plateauSection');
            
                if (!this.sessionData.plateauOrders || this.sessionData.plateauOrders.length === 0) {
                    plateauSection.style.display = 'none';
                    return;
                }

                plateauSection.style.display = 'block';
                plateauContainer.innerHTML = '';

                this.sessionData.plateauOrders.forEach(order => {
                    const isServed = order.status === 'served';
                    const toggleClass = isServed ? 'served' : '';

                    // BILINGUAL STATUS LIKE GUEST SIDE
                    const toggleLabel = isServed
                        ? '<span class="french-text">Servi</span><span class="english-text">Served</span>'
                        : '<span class="french-text">Commandé</span><span class="english-text">Ordered</span>';

                    const plateauItem = document.createElement('div');
                    plateauItem.className = 'plateau-item';
                    plateauItem.innerHTML = `
                        <div class="plateau-icon"></div>
                        <div class="plateau-details">
                            <div class="plateau-name french-text">${order.nameFr}</div>
                            <div class="plateau-name english-text">${order.nameEn || order.nameFr}</div>
                            <div class="plateau-quantity french-text">${order.plateauCount}x pour le groupe</div>
                            <div class="plateau-quantity english-text">${order.plateauCount}x for group</div>
                        </div>
                        <div class="serving-toggle plateau-toggle ${toggleClass}" 
                            data-order-id="${order.id}" 
                            data-status="${order.status}">
                            <div class="toggle-slider">${isServed ? '✓' : '○'}</div>
                            <span class="toggle-label">${toggleLabel}</span>
                        </div>
                    `;
                    
                    plateauContainer.appendChild(plateauItem);
                });

                // Add event listeners for plateau toggles
                document.querySelectorAll('.plateau-toggle').forEach(toggle => {
                    this.addDragToggleBehavior(toggle, (orderId, currentStatus) => {
                        this.togglePlateauStatus(orderId, currentStatus);
                    });
                });
            }

            getGuestDisplayName(guestName, language, guestNumber) {
                // If guest has a real name (doesn't contain "/"), use it
                if (guestName && !guestName.includes('/')) {
                    return guestName;
                }
                
                // Otherwise show clean fallback based on language
                return language === 'fr' ? `Invité ${guestNumber}` : `Guest ${guestNumber}`;
            }

            renderWineryHeader() {
                if (!this.sessionData.winery) return;
                
                document.getElementById('sessionWineryName').textContent = this.sessionData.winery.name;

                const logoElement = document.getElementById('sessionWineryLogo');
                if (PremiumFeatures.hasFeature(this.sessionData.winery, 'logo_display') && this.sessionData.winery.logo) {
                    logoElement.innerHTML = `<img src="${this.sessionData.winery.logo}" alt="${this.sessionData.winery.name}">`;
                    logoElement.style.display = 'block';
                } else {
                    logoElement.style.display = 'none';
                    document.querySelector('.session-header-info h1').style.marginLeft = '10px';
                }
            }

            getWineryInitials(name) {
                return name.split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
            }

                // Enhanced drag-to-toggle functionality
                addDragToggleBehavior(toggle, callback) {
                    const slider = toggle.querySelector('.toggle-slider');
                    let isDragging = false;
                    let startX = 0;
                    let sliderStartLeft = 0;
                    let hasDraggedMinimum = false;
                    const MINIMUM_DRAG_DISTANCE = 15; // pixels
                    
                    function startDrag(e) {
                        isDragging = true;
                        hasDraggedMinimum = false;
                        slider.style.cursor = 'grabbing';
                        
                        // Update current state at drag start
                        const currentlyOn = toggle.classList.contains('served');
                        
                        const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                        startX = clientX;
                        
                        const sliderRect = slider.getBoundingClientRect();
                        const toggleRect = toggle.getBoundingClientRect();
                        sliderStartLeft = sliderRect.left - toggleRect.left;
                        
                        e.preventDefault();
                        e.stopPropagation();
                    }

                    function drag(e) {
                        if (!isDragging) return;
                        
                        const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                        const deltaX = clientX - startX;
                        let newLeft = sliderStartLeft + deltaX;
                        
                        // Check if minimum drag distance has been reached
                        if (Math.abs(deltaX) >= MINIMUM_DRAG_DISTANCE) {
                            hasDraggedMinimum = true;
                        }
                        
                        // Constrain within toggle bounds (2px to 134px based on your CSS)
                        newLeft = Math.max(2, Math.min(134, newLeft));
                        
                        slider.style.left = newLeft + 'px';
                        slider.style.transition = 'none';
                        e.preventDefault();
                    }

                    function endDrag(e) {
                        if (!isDragging) return;
                        
                        isDragging = false;
                        slider.style.cursor = 'grab';
                        slider.style.transition = ''; // Re-enable transition
                        
                        // Only toggle if user has dragged the minimum distance
                        if (hasDraggedMinimum) {
                            const sliderRect = slider.getBoundingClientRect();
                            const toggleRect = toggle.getBoundingClientRect();
                            const sliderCenter = sliderRect.left + sliderRect.width / 2 - toggleRect.left;
                            const toggleWidth = toggleRect.width;
                            
                            // Get current state from CSS class
                            const isCurrentlyOn = toggle.classList.contains('served');
                            
                            // EXACT logic from your working slider
                            const shouldBeOn = isCurrentlyOn ? 
                                sliderCenter > toggleWidth * 0.3 : 
                                sliderCenter > toggleWidth * 0.7;
                                
                            if (shouldBeOn !== isCurrentlyOn) {
                                // Update visual state immediately
                                if (shouldBeOn) {
                                    toggle.classList.add('served');
                                    toggle.dataset.status = 'served';
                                    
                                    // Update text immediately
                                    const label = toggle.querySelector('.toggle-label');
                                    label.innerHTML = '<span class="french-text">Servi</span><span class="english-text">Served</span>';
                                } else {
                                    toggle.classList.remove('served');
                                    toggle.dataset.status = 'ordered';
                                    
                                    // Update text immediately
                                    const label = toggle.querySelector('.toggle-label');
                                    label.innerHTML = '<span class="french-text">Commandé</span><span class="english-text">Ordered</span>';
                                }
                                
                                // Make API call
                                const orderId = toggle.dataset.orderId;
                                const currentStatus = shouldBeOn ? 'ordered' : 'served';
                                callback(orderId, currentStatus);
                            }
                        }
                        
                        // Reset inline style to use CSS transitions (exactly like working version)
                        slider.style.left = '';
                    }

                    // Mouse events
                    toggle.addEventListener('mousedown', startDrag);
                    document.addEventListener('mousemove', drag);
                    document.addEventListener('mouseup', endDrag);

                    // Touch events for mobile/tablet
                    toggle.addEventListener('touchstart', startDrag);
                    document.addEventListener('touchmove', drag);
                    document.addEventListener('touchend', endDrag);

                    // Prevent context menu
                    toggle.addEventListener('contextmenu', e => e.preventDefault());
                }

            async toggleWineStatus(orderId, currentStatus) {
                const newStatus = currentStatus === 'served' ? 'ordered' : 'served';
                
                try {
                    const response = await fetch(`${this.API_BASE}/host/wine-status`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            wine_order_id: parseInt(orderId),
                            status: newStatus,
                            served_by: 'Host'
                        })
                    });

                    if (response.ok) {
                        await this.loadSessionData();
                    } else {
                        const error = await response.json();
                        throw new Error(error.error || 'Failed to update wine status');
                    }

                } catch (error) {
                    console.error('Error updating wine status:', error);
                    alert('Failed to update wine status: ' + error.message);
                }
            }

            async togglePlateauStatus(orderId, currentStatus) {
                const newStatus = currentStatus === 'served' ? 'ordered' : 'served';
                
                try {
                    const response = await fetch(`${this.API_BASE}/host/plateau-status`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            plateau_order_id: parseInt(orderId),
                            status: newStatus,
                            served_by: 'Host'
                        })
                    });

                    if (response.ok) {
                        await this.loadSessionData();
                    } else {
                        const error = await response.json();
                        throw new Error(error.error || 'Failed to update plateau status');
                    }

                } catch (error) {
                    console.error('Error updating plateau status:', error);
                    alert('Failed to update plateau status: ' + error.message);
                }
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.loadSessionData().catch(error => {
                        console.error('Auto-refresh failed:', error);
                    });
                }, 30000);
            }

            showMainContent() {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
            }

            showError(message) {
                document.getElementById('loadingState').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #dc2626;">
                        <h3>⚠️ ${message}</h3>
                        <p>
                            <span class="french-text">Session introuvable ou erreur de chargement.</span>
                            <span class="english-text">Session not found or loading error.</span>
                        </p>
                        <button class="back-btn" style="margin-top: 15px; padding: 10px 20px; background: var(--wine-dark); color: white; border: none; border-radius: 8px; cursor: pointer;" onclick="window.location.href='dashboard.html'">
                            <span class="french-text">Retour au tableau de bord</span><span class="english-text">Back to dashboard</span>
                        </button>
                    </div>
                `;
            }

            cleanup() {
                // Clean up auto-refresh
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                }
                
                // Remove document-level drag listeners
                document.removeEventListener('mousemove', this.globalDragHandler);
                document.removeEventListener('mouseup', this.globalEndDragHandler);
                document.removeEventListener('touchmove', this.globalDragHandler);
                document.removeEventListener('touchend', this.globalEndDragHandler);
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.hostSessionManagement) {
                window.hostSessionManagement.cleanup();
            }
        });

        // Initialize when page loads - WITH FOUC PREVENTION
        window.addEventListener('load', () => {
            document.body.classList.add('loaded'); // Show content after CSS loads
            window.hostSessionManagement = new HostSessionManagement();
        });
        
        // Initialize when page loads - WITH FOUC PREVENTION
        window.addEventListener('load', () => {
            document.body.classList.add('loaded'); // Show content after CSS loads
            new HostSessionManagement();
        });
    </script>
</body>
</html>
