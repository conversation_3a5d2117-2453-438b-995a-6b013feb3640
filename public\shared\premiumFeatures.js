// shared/premiumFeatures.js
class PremiumFeatures {
    static hasFeature(winery, featureName) {
        const premiumFeatures = {
            'logo_display': winery.subscription_tier === 'premium',
            'advanced_analytics': winery.subscription_tier === 'premium', 
            'email_collection': winery.subscription_tier === 'premium',
            'custom_branding': winery.subscription_tier === 'premium',
            'api_access': winery.subscription_tier === 'premium',
            'priority_support': winery.subscription_tier === 'premium'
        };
        
        return premiumFeatures[featureName] || false;
    }

    static getFeatureList(tier) {
        const allFeatures = {
            'freemium': ['basic_session_management', 'wine_ordering', 'plateau_ordering'],
            'premium': ['logo_display', 'advanced_analytics', 'email_collection', 'custom_branding', 'api_access', 'priority_support']
        };
        
        return allFeatures[tier] || [];
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PremiumFeatures;
}