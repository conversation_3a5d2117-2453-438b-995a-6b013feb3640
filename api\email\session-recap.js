import nodemailer from 'nodemailer';
import { query } from '../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        // Check if email feature is enabled
        if (process.env.EMAIL_FEATURE_ENABLED !== 'true') {
            return res.status(503).json({ 
                error: 'Email feature is currently disabled',
                featureDisabled: true 
            });
        }

        const { groupCode, guestEmail, language } = req.body;

        if (!groupCode || !guestEmail) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(guestEmail)) {
            return res.status(400).json({ error: 'Invalid email address' });
        }

        // Fetch session data from database
        const sessionResult = await query(
            `SELECT 
                ts.*,
                w.name as winery_name,
                w.address as winery_address,
                w.phone as winery_phone,
                w.email as winery_email,
                w.logo as winery_logo,
                w.subscription_tier as subscription_tier
            FROM tasting_sessions ts
            JOIN wineries w ON ts.winery_id = w.id
            WHERE ts.group_code = $1`,
            [groupCode]
        );

        if (sessionResult.rows.length === 0) {
            return res.status(404).json({ error: 'Session not found' });
        }

        const session = sessionResult.rows[0];

        // Fetch guests and their wine orders
        const guestsQuery = `
            SELECT 
                g.*,
                json_agg(
                    json_build_object(
                        'wine_name', w.name,
                        'wine_category_fr', w.category,
                        'wine_category_en', w.category,
                        'status', wo.status
                    ) ORDER BY wo.ordered_at
                ) FILTER (WHERE wo.id IS NOT NULL) as wine_orders
            FROM guests g
            LEFT JOIN wine_orders wo ON g.id = wo.guest_id
            LEFT JOIN wines w ON wo.wine_id = w.id
            WHERE g.session_id = $1
            GROUP BY g.id
            ORDER BY g.guest_number
        `;
        
        const guestsResult = await query(guestsQuery, [session.id]);

        // Fetch plateau orders
        const plateauxQuery = `
            SELECT 
                p.name_fr,
                p.name_en,
                po.plateau_count,
                po.status
            FROM plateau_orders po
            JOIN plateaux p ON po.plateau_id = p.id
            WHERE po.session_id = $1
        `;
        
        const plateauxResult = await query(plateauxQuery, [session.id]);

        // Build email HTML
        const emailHTML = buildEmailTemplate({
            session,
            guests: guestsResult.rows,
            plateaux: plateauxResult.rows,
            language: language || 'fr'
        });

        // Configure SMTP transporter
        const transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST,
            port: parseInt(process.env.SMTP_PORT),
            secure: false,
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });

        // Send email
        const isFrench = language === 'fr';
        const subject = isFrench 
            ? `Dégustation chez ${session.winery_name} - ${session.group_name}`
            : `Tasting at ${session.winery_name} - ${session.group_name}`;

        await transporter.sendMail({
            from: `"EZvino" <${process.env.SMTP_FROM}>`,
            to: guestEmail,
            subject: subject,
            html: emailHTML
        });

        res.status(200).json({ 
            success: true, 
            message: 'Email sent successfully' 
        });

    } catch (error) {
        console.error('Email send error:', error);
        res.status(500).json({ 
            error: 'Failed to send email',
            details: error.message 
        });
    }
}

function buildEmailTemplate({ session, guests, plateaux, language }) {
    const isFrench = language === 'fr';
    
    const sessionDate = new Date(session.created_at).toLocaleDateString(
        isFrench ? 'fr-CA' : 'en-CA',
        { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }
    );

    // Build wines list by guest
    let winesHTML = '';
    guests.forEach(guest => {
        if (guest.wine_orders && guest.wine_orders.length > 0) {
            winesHTML += `
                <div style="margin-bottom: 20px;">
                    <h3 style="color: #64748b; font-size: 14px; margin-bottom: 10px;">${guest.guest_name}</h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
            `;
            
            guest.wine_orders.forEach(wine => {
                const category = isFrench ? wine.wine_category_fr : wine.wine_category_en;
                winesHTML += `
                    <li style="padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                        ${wine.wine_name} <span style="color: #94a3b8; font-size: 12px;">(${category})</span>
                    </li>
                `;
            });
            
            winesHTML += `
                    </ul>
                </div>
            `;
        }
    });

    // Build plateaux list
    let plateauxHTML = '';
    if (plateaux && plateaux.length > 0) {
        plateauxHTML = `
            <div style="margin-top: 30px;">
                <h2 style="color: #1e293b; font-size: 18px; margin-bottom: 15px;">
                    ${isFrench ? 'Plateaux' : 'Boards'}
                </h2>
                <ul style="list-style: none; padding: 0; margin: 0;">
        `;
        
        plateaux.forEach(plateau => {
            const name = isFrench ? plateau.name_fr : plateau.name_en;
            plateauxHTML += `
                <li style="padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                    ${name} <span style="color: #94a3b8;">(${plateau.plateau_count}x)</span>
                </li>
            `;
        });
        
        plateauxHTML += `
                </ul>
            </div>
        `;
    }

    // Build complete email HTML with inline CSS
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: white;">
        
        <!-- Header -->
        <div style="background-color: #7c2d12; color: white; padding: 30px 20px; text-align: center;">
            ${session.subscription_tier === 'premium' && session.winery_logo
                ? `<img src="${session.winery_logo}" alt="${session.winery_name}" style="max-width: 200px; height: auto; margin-bottom: 15px; background: white; padding: 10px; border-radius: 8px;">` 
                : `<img src="https://jap8t59igsawm5hl.public.blob.vercel-storage.com/ezvino-logo-eml.png" alt="EZvino" style="max-width: 200px; height: auto; margin-bottom: 15px;">`
            }
            <h1 style="margin: 0; font-size: 24px;">
                ${isFrench ? 'Récapitulatif de dégustation' : 'Tasting Recap'}
            </h1>
            <p style="margin: 10px 0 0 0; font-size: 16px;">${session.winery_name}</p>
        </div>

        <!-- Content -->
        <div style="padding: 30px 20px;">
            
            <!-- Session Info -->
            <div style="background-color: #f8fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 30px; text-align: center;">
                <h2 style="margin: 0 0 10px 0; color: #1e293b; font-size: 20px;">${session.group_name}</h2>
                <p style="margin: 5px 0; color: #64748b;">
                    ${isFrench ? 'Groupe de' : 'Party of'} ${session.guest_count}
                </p>
                <p style="margin: 5px 0; color: #94a3b8; font-size: 14px;">${sessionDate}</p>
            </div>

            <!-- Wines -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #1e293b; font-size: 18px; margin-bottom: 15px;">
                    ${isFrench ? 'Vins dégustés' : 'Wines Tasted'}
                </h2>
                ${winesHTML}
            </div>

            <!-- Plateaux -->
            ${plateauxHTML}

            <!-- Winery Contact -->
            ${session.winery_email || session.winery_phone ? `
            <div style="margin-top: 40px; padding: 20px; background-color: #f8fafc; border-radius: 8px; text-align: center;">
                <h3 style="color: #64748b; font-size: 16px; margin: 0 0 15px 0;">
                    ${isFrench ? 'Questions?' : 'Questions?'}
                </h3>
                <p style="margin: 0 0 10px 0; color: #1e293b; font-size: 14px;">
                    ${isFrench ? 'Contactez' : 'Contact'}: ${session.winery_name}
                </p>
                ${session.winery_email ? `<p style="margin: 5px 0; color: #1e293b; font-size: 14px;">${session.winery_email}</p>` : ''}
                ${session.winery_phone ? `<p style="margin: 5px 0; color: #1e293b; font-size: 14px;">${session.winery_phone}</p>` : ''}
            </div>
            ` : ''}

        </div>

        <!-- Footer -->
        <div style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e2e8f0;">
            <img src="https://jap8t59igsawm5hl.public.blob.vercel-storage.com/ezvino-logo-eml.png" alt="EZvino" style="max-width: 80px; height: auto; margin-bottom: 10px; opacity: 0.6;">
            <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                ${isFrench ? 'Propulsé par' : 'Powered by'} <a href="https://ezvino.app" style="color: #7c2d12; text-decoration: none;">EZvino.app</a>
            </p>
        </div>

    </div>
</body>
</html>
    `;
}