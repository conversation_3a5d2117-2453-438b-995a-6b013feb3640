// api/admin/wines.js
// Clean version with categories endpoint properly positioned

import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  try {
    // Simple auth check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    // Categories endpoint - FIRST, no winery required
    if (req.query.action === 'categories') {
      try {
        const categories = await query(`
          SELECT category_code, name_fr, name_en, sort_order 
          FROM wine_categories 
          WHERE is_active = true 
          ORDER BY sort_order
        `);
        
        return res.json({
          success: true,
          categories: categories.rows
        });
      } catch (error) {
        return res.status(500).json({ error: 'Failed to load categories' });
      }
    }

    // GET - List wines for a winery
    if (req.method === 'GET') {
      const { winery } = req.query;
      
      if (!winery) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID is required'
        });
      }

      const queryText = `
        SELECT * FROM wines 
        WHERE winery_id = $1 
        ORDER BY sort_order ASC, name ASC
      `;

      const result = await query(queryText, [winery]);

      return res.status(200).json({
        success: true,
        wines: result.rows
      });
    }

    // POST - Create new wine
    if (req.method === 'POST') {
      const {
        wineryId,
        name,
        category,
        description_fr,
        description_en,
        price,
        alcohol_content,
        vintage,
        sort_order
      } = req.body;

      // Validation
      if (!wineryId || !name || !category) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID, name, and category are required'
        });
      }

      // Verify winery exists
      const wineryCheck = await query(
        'SELECT id FROM wineries WHERE id = $1 AND is_active = true',
        [wineryId]
      );

      if (!wineryCheck.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Winery not found'
        });
      }

      const insertQuery = `
        INSERT INTO wines (
          winery_id, name, category, description_fr, description_en,
          price, alcohol_content, vintage, sort_order, is_available
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, true)
        RETURNING *
      `;

      const values = [
        wineryId,
        name.trim(),
        category,
        description_fr?.trim() || null,
        description_en?.trim() || null,
        price ? parseFloat(price) : null,
        alcohol_content ? parseFloat(alcohol_content) : null,
        vintage ? parseInt(vintage) : null,
        sort_order ? parseInt(sort_order) : 0
      ];

      const result = await query(insertQuery, values);

      return res.status(201).json({
        success: true,
        wine: result.rows[0],
        message: 'Wine created successfully'
      });
    }

    // PATCH - Update existing wine
    if (req.method === 'PATCH') {
      const { id: wineId } = req.query;
      
      if (!wineId || wineId === 'wines.js') {
        return res.status(400).json({
          success: false,
          error: 'Wine ID is required'
        });
      }

      const {
        name,
        category,
        description_fr,
        description_en,
        price,
        alcohol_content,
        vintage,
        sort_order,
        is_available
      } = req.body;

      // Build dynamic update query
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      if (name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(name.trim());
      }
      if (category !== undefined) {
        updateFields.push(`category = $${paramIndex++}`);
        updateValues.push(category);
      }
      if (description_fr !== undefined) {
        updateFields.push(`description_fr = $${paramIndex++}`);
        updateValues.push(description_fr?.trim() || null);
      }
      if (description_en !== undefined) {
        updateFields.push(`description_en = $${paramIndex++}`);
        updateValues.push(description_en?.trim() || null);
      }
      if (price !== undefined) {
        updateFields.push(`price = $${paramIndex++}`);
        updateValues.push(price ? parseFloat(price) : null);
      }
      if (alcohol_content !== undefined) {
        updateFields.push(`alcohol_content = $${paramIndex++}`);
        updateValues.push(alcohol_content ? parseFloat(alcohol_content) : null);
      }
      if (vintage !== undefined) {
        updateFields.push(`vintage = $${paramIndex++}`);
        updateValues.push(vintage ? parseInt(vintage) : null);
      }
      if (sort_order !== undefined) {
        updateFields.push(`sort_order = $${paramIndex++}`);
        updateValues.push(sort_order ? parseInt(sort_order) : 0);
      }
      if (is_available !== undefined) {
        updateFields.push(`is_available = $${paramIndex++}`);
        updateValues.push(Boolean(is_available));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No fields to update'
        });
      }

      // Add updated timestamp
      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(wineId);

      const updateQuery = `
        UPDATE wines 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;

      const result = await query(updateQuery, updateValues);

      if (!result.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Wine not found'
        });
      }

      return res.status(200).json({
        success: true,
        wine: result.rows[0],
        message: 'Wine updated successfully'
      });
    }

    // DELETE - Remove wine
    if (req.method === 'DELETE') {
      const { id, hard } = req.query;
      
      if (hard === 'true') {
        // HARD DELETE - Complete removal
        const result = await query('DELETE FROM wines WHERE id = $1', [id]);
        
        if (result.rowCount === 0) {
          return res.status(404).json({ error: 'Wine not found' });
        }
        
        return res.json({ 
          success: true, 
          message: 'Wine permanently deleted from database'
        });
      } else {
        // SOFT DELETE
        const result = await query(
          'UPDATE wines SET is_available = false WHERE id = $1', 
          [id]
        );
        
        if (result.rowCount === 0) {
          return res.status(404).json({ error: 'Wine not found' });
        }
        
        return res.json({ 
          success: true, 
          message: 'Wine made unavailable (can be restored)'
        });
      }
    }

    // PUT - Toggle wine availability
    if (req.method === 'PUT') {
      const { action, id } = req.query;
      
      if (action === 'toggle' && id) {
        const toggleQuery = `
          UPDATE wines 
          SET is_available = NOT is_available, updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          RETURNING *
        `;

        const result = await query(toggleQuery, [id]);

        if (!result.rows.length) {
          return res.status(404).json({
            success: false,
            error: 'Wine not found'
          });
        }

        return res.status(200).json({
          success: true,
          wine: result.rows[0],
          message: `Wine ${result.rows[0].is_available ? 'enabled' : 'disabled'}`
        });
      }
    }

    // Method not allowed
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });

  } catch (error) {
    console.error('Wine management error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}