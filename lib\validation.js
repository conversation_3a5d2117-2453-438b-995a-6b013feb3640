// =============================================
// FILE: backend/middleware/validation.js
// Complete validation middleware functions
// =============================================

// Basic validation middleware functions
const validateRequired = (fields, source = 'body') => {
    return (req, res, next) => {
        const data = source === 'params' ? req.params : req.body;
        const missing = [];

        fields.forEach(field => {
            if (!data[field] && data[field] !== 0) {
                missing.push(field);
            }
        });

        if (missing.length > 0) {
            return res.status(400).json({
                error: 'Missing required fields',
                missing_fields: missing
            });
        }

        next();
    };
};

const validateInteger = (fields, source = 'body') => {
    return (req, res, next) => {
        const data = source === 'params' ? req.params : req.body;
        const invalid = [];

        fields.forEach(field => {
            if (data[field] !== undefined) {
                const value = source === 'params' ? parseInt(data[field]) : data[field];
                if (isNaN(value) || !Number.isInteger(Number(value))) {
                    invalid.push(field);
                }
                // Convert to integer for params
                if (source === 'params') {
                    data[field] = value;
                }
            }
        });

        if (invalid.length > 0) {
            return res.status(400).json({
                error: 'Invalid integer values',
                invalid_fields: invalid
            });
        }

        next();
    };
};

const validateEnum = (field, allowedValues, source = 'body') => {
    return (req, res, next) => {
        const data = source === 'params' ? req.params : req.body;
        const value = data[field];

        if (value !== undefined && !allowedValues.includes(value)) {
            return res.status(400).json({
                error: `Invalid value for ${field}`,
                allowed_values: allowedValues,
                received: value
            });
        }

        next();
    };
};

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
    // Sanitize string inputs to prevent XSS
    const sanitizeString = (str) => {
        if (typeof str !== 'string') return str;
        return str.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    };

    const sanitizeObject = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                obj[key] = sanitizeString(obj[key]);
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitizeObject(obj[key]);
            }
        }
    };

    if (req.body) sanitizeObject(req.body);
    if (req.query) sanitizeObject(req.query);
    if (req.params) sanitizeObject(req.params);

    next();
};

module.exports = {
    validateRequired,
    validateInteger,
    validateEnum,
    sanitizeInput
};