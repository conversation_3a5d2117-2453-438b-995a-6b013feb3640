{"version": 2, "functions": {"api/**/*.js": {"maxDuration": 30}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/session.html", "destination": "/guest/session.html"}, {"source": "/session/(.*)", "destination": "/guest/session.html"}, {"source": "/host", "destination": "/host/dashboard.html"}, {"source": "/host/(.*)", "destination": "/host/$1"}, {"source": "/admin", "destination": "/admin/index.html"}, {"source": "/admin/(.*)", "destination": "/admin/$1"}, {"source": "/", "destination": "/landing.html", "has": [{"type": "host", "value": "^preview\\.ezvino\\.app$"}]}, {"source": "/", "destination": "/landing.html", "has": [{"type": "host", "value": "^(ezvino\\.app|www\\.ezvino\\.app)$"}]}, {"source": "/(.*)", "destination": "/guest/index.html", "has": [{"type": "host", "value": "^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}\\.(preview\\.)?ezvino\\.app$"}]}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*.ezvino.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Winery-Slug"}]}]}