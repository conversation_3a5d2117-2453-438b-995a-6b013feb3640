/* =============================================
   EZVINO MASTER STYLESHEET V2.0
   Updated with Wine Prototype Color Palette
   ============================================= */

:root {
    /* PRIMARY WINE COLORS (from prototype) */
    --wine-red-primary: #b0aeae;        /* Deep wine red - main brand color */
    --wine-red-secondary: #b0aeae;      /* Brown red - secondary brand color */
    --wine-dark: #2d1b1e;              /* Keep existing for contrast */
    --wine-gold: #ffffff;              /* Keep existing gold accent */
    --wine-cream: #f5f5dc;             /* Keep existing cream */
    
    /* BACKGROUND COLORS (from prototype) */
    --bg-primary: #f8f9fa;             /* Light gray - main background */
    --bg-secondary: #ffffff;           /* Pure white - card backgrounds */
    --bg-tertiary: #e9ecef;            /* Lighter gray - subtle backgrounds */
    --bg-gradient-start: #667eea;      /* Blue gradient start */
    --bg-gradient-end: #764ba2;        /* Purple gradient end */
    
    /* INTERACTIVE COLORS (from prototype) */
    --interactive-primary: #007bff;     /* Bootstrap blue - primary buttons */
    --interactive-primary-hover: #0056b3; /* Darker blue - hover state */
    --interactive-secondary: #6c757d;   /* Gray - secondary buttons */
    --interactive-secondary-hover: #545b62; /* Darker gray - hover state */
    
    /* SUCCESS & STATUS COLORS (from prototype) */
    --success-green: #b0aeae;          /* Success green */
    --success-green-hover: #20c997;    /* Success green hover */
    --success-bg: #d4edda;             /* Success background */
    --success-border: #c3e6cb;         /* Success border */
    --success-text: #155724;           /* Success text */
    
    --warning-yellow: #ffc107;         /* Warning yellow */
    --warning-text: #212529;           /* Warning text */
    
    /* TEXT COLORS (from prototype) */
    --text-primary: #333333;           /* Primary text - dark gray */
    --text-secondary: #666666;         /* Secondary text - medium gray */
    --text-tertiary: #6c757d;          /* Tertiary text - light gray */
    --text-white: #ffffff;             /* White text */
    --text-wine: var(--wine-red-primary); /* Wine colored text */
    
    /* FRENCH/ENGLISH TEXT (keep existing) */
    --french-text: #000000;            /* Black for French */
    --english-text: #dc2626;           /* Red for English */
    
    /* BORDER COLORS (from prototype) */
    --border-light: #e9ecef;           /* Light borders */
    --border-medium: #e5e7eb;          /* Medium borders */
    --border-wine: var(--wine-red-primary); /* Wine accent borders */
    
    /* SHADOW PRESETS (from prototype) */
    --shadow-light: 0 4px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
    --shadow-subtle: 0 2px 10px rgba(0,0,0,0.1);
    
    /* GRADIENT DEFINITIONS (from prototype) */
    --gradient-wine: linear-gradient(135deg, var(--wine-red-primary) 0%, var(--wine-red-secondary) 100%);
    --gradient-background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-green) 0%, var(--success-green-hover) 100%);
    --gradient-interactive: linear-gradient(135deg, var(--interactive-primary) 0%, var(--interactive-primary-hover) 100%);
    
    /* SPACING & SIZING */
    --border-radius-small: 8px;
    --border-radius-medium: 12px;
    --border-radius-large: 15px;
    --border-radius-full: 50%;
    
    /* TRANSITIONS */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* =============================================
   GLOBAL STYLES
   ============================================= */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
}

/* =============================================
   LAYOUT COMPONENTS
   ============================================= */

.app-container {
    max-width: 414px;
    margin: 0 auto;
    min-height: 100vh;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-medium);
    position: relative;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
}

.main-content {
    padding: 20px;
}

.section {
    margin-bottom: 25px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-subtle);
}

.section-header {
    background: var(--gradient-wine);
    color: var(--text-white);
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-content {
    padding: 20px;
}

/* =============================================
   HEADER STYLES
   ============================================= */

.header {
    background: var(--gradient-wine);
    color: var(--text-white);
    padding: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="header-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23header-pattern)"/></svg>') repeat;
    opacity: 0.3;
}

.header-icon {
    font-size: 36px;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* =============================================
   BUTTON STYLES
   ============================================= */

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius-small);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    min-width: 120px;
}

.btn-primary {
    background: var(--gradient-interactive);
    color: var(--text-white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--interactive-secondary);
    color: var(--text-white);
}

.btn-secondary:hover {
    background: var(--interactive-secondary-hover);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--text-white);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-wine {
    background: var(--gradient-wine);
    color: var(--text-white);
}

.btn-wine:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
}

.btn:disabled {
    background: var(--interactive-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

.btn-large {
    padding: 15px 40px;
    font-size: 1.1rem;
    border-radius: 25px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
    min-width: auto;
}

/* =============================================
   FORM STYLES
   ============================================= */

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-small);
    font-size: 16px;
    transition: var(--transition-normal);
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--interactive-primary);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control:invalid {
    border-color: #dc3545;
}

/* =============================================
   CARD STYLES
   ============================================= */

.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-medium);
    padding: 20px;
    box-shadow: var(--shadow-light);
    margin-bottom: 20px;
}

.card-header {
    background: var(--bg-primary);
    margin: -20px -20px 20px -20px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-light);
    font-weight: 600;
    color: var(--text-primary);
}

.card-wine {
    border-left: 4px solid var(--wine-red-primary);
}

.card-interactive {
    cursor: pointer;
    transition: var(--transition-normal);
}

.card-interactive:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: var(--interactive-primary);
}

/* =============================================
   STATUS & BADGES
   ============================================= */

.status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-ordered {
    background: var(--warning-yellow);
    color: var(--warning-text);
}

.status-served {
    background: var(--success-bg);
    color: var(--success-text);
}

.status-pending {
    background: var(--warning-yellow);
    color: var(--warning-text);
}

.status-delivered {
    background: var(--success-green);
    color: var(--text-white);
}

.status-tasted {
    background: var(--wine-red-primary);
    color: var(--text-white);
}

/* =============================================
   GRID LAYOUTS
   ============================================= */

.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

/* =============================================
   WINE-SPECIFIC COMPONENTS
   ============================================= */

.wine-grid {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    gap: 15px;
    margin-top: 30px;
    background: var(--bg-primary);
    padding: 20px;
    border-radius: var(--border-radius-medium);
}

.wine-item {
    font-weight: 600;
    padding: 12px 10px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-small);
    border-left: 4px solid var(--wine-red-primary);
}

.wine-name {
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.wine-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 400;
    font-style: italic;
}

.wine-checkbox {
    text-align: center;
    padding: 10px;
}

.wine-checkbox input {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

/* =============================================
   BILINGUAL TEXT SUPPORT
   ============================================= */

.french-text {
    color: var(--french-text);
}

.english-text {
    color: var(--english-text);
    font-style: italic !important;
}

.bilingual-separator {
    margin: 0 8px;
    color: var(--text-secondary);
}

/* =============================================
   LOADING & STATES
   ============================================= */

.loading {
    text-align: center;
    padding: 40px;
    color: var(--text-tertiary);
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--wine-red-primary);
    border-radius: var(--border-radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-message {
    background: var(--success-bg);
    color: var(--success-text);
    padding: 20px;
    border-radius: var(--border-radius-small);
    margin-top: 20px;
    border: 1px solid var(--success-border);
    text-align: center;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: var(--border-radius-small);
    margin-top: 20px;
    border: 1px solid #f5c6cb;
    text-align: center;
}

/* =============================================
   RESPONSIVE DESIGN
   ============================================= */

@media (max-width: 768px) {
    .app-container {
        max-width: 100%;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .wine-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .header-title {
        font-size: 1.8rem;
    }
    
    .btn-large {
        padding: 12px 24px;
        font-size: 1rem;
    }
}

@media (max-width: 375px) {
    .main-content {
        padding: 12px;
    }
    
    .section-content {
        padding: 15px;
    }
}

/* =============================================
   UTILITY CLASSES
   ============================================= */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-wine { color: var(--wine-red-primary); }
.text-success { color: var(--success-green); }
.text-secondary { color: var(--text-secondary); }
.text-white { color: var(--text-white); }

.bg-wine { background: var(--wine-red-primary); }
.bg-success { background: var(--success-green); }
.bg-light { background: var(--bg-primary); }
.bg-white { background: var(--bg-secondary); }

.border-wine { border-color: var(--wine-red-primary); }
.border-light { border-color: var(--border-light); }

.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.p-1 { padding: 10px; }
.p-2 { padding: 20px; }
.p-3 { padding: 30px; }

.hidden { display: none; }
.visible { display: block; }