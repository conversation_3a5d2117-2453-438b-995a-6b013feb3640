// api/admin/auth.js
// Simple admin authentication endpoint

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { password } = req.body;

    // Validate input
    if (!password) {
      return res.status(400).json({ 
        success: false, 
        error: 'Password is required' 
      });
    }

    // Get admin password from environment
    const correctPassword = process.env.ADMIN_PASSWORD || 'WineAdmin2025!';

    // Simple password comparison
    if (password === correctPassword) {
      // Generate simple session token
      const timestamp = Date.now();
      const token = Buffer.from(`admin:${timestamp}:${Math.random()}`).toString('base64');
      
      // Set session cookie (optional - using localStorage in frontend)
      res.setHeader('Set-Cookie', [
        `adminAuth=${token}; HttpOnly; Path=/admin; Max-Age=3600; SameSite=Strict`,
        `adminAuthTime=${timestamp}; HttpOnly; Path=/admin; Max-Age=3600; SameSite=Strict`
      ]);

      return res.status(200).json({
        success: true,
        token,
        message: 'Authentication successful',
        expiresIn: 3600 // 1 hour
      });
    } else {
      // Invalid password
      return res.status(401).json({
        success: false,
        error: 'Invalid admin password'
      });
    }
  } catch (error) {
    console.error('Admin auth error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication service error'
    });
  }
}