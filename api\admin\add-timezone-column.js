import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Adding timezone column...');

    // Add timezone column
    await query(`
      ALTER TABLE wineries 
      ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'America/Montreal';
    `);

    // Update existing records to have default timezone
    const updateResult = await query(`
      UPDATE wineries 
      SET timezone = 'America/Montreal' 
      WHERE timezone IS NULL;
    `);

    console.log(`Updated ${updateResult.rowCount} existing records with default timezone`);

    res.status(200).json({ 
      success: true, 
      message: 'Timezone column added successfully',
      updated_records: updateResult.rowCount
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({ 
      error: 'Migration failed: ' + error.message 
    });
  }
}