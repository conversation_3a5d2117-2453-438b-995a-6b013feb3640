<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EZvino Admin - Login</title>
    <link rel="stylesheet" href="../admin/adminstyle.css">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-logo">
                <img src="../shared/assets/EZvino_logo.png" alt="EZvino">
            </div>
            <h1 class="auth-title">EZvino Admin Portal</h1>
            <p class="auth-subtitle">Sign in with your authorized Google account</p>
            
            <div class="error-message" id="errorMessage" style="display: none;"></div>
            <div class="success-message" id="successMessage" style="display: none;"></div>
            
            <button type="button" class="google-login-btn" id="googleLoginBtn">
                <div class="google-icon"></div>
                <span>Sign in with Google</span>
            </button>
            
            <div class="auth-footer">
                EZvino Wine Management System V2.0<br>
                Authorized access: Jeff & May
            </div>
        </div>
    </div>

    <script>
        // OAuth token handling
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const name = urlParams.get('name');
        const error = urlParams.get('error');

        // Handle OAuth callback
        if (token && name) {
            localStorage.setItem('adminAuth', token);
            localStorage.setItem('adminAuthTime', Date.now().toString());
            window.location.href = '/admin/index.html';
        }

        // Handle OAuth errors
        if (error) {
            const errorMessages = {
                'access_denied': 'Access denied. Please try again.',
                'unauthorized': 'Your Google account is not authorized for admin access.',
                'server_error': 'Server error. Please try again.',
                'no_code': 'Authentication failed. Please try again.'
            };
            document.getElementById('errorMessage').textContent = errorMessages[error] || 'Authentication failed.';
            document.getElementById('errorMessage').style.display = 'block';
        }

        // Check if already authenticated
        function isAuthenticated() {
            const token = localStorage.getItem('adminAuth');
            const authTime = localStorage.getItem('adminAuthTime');
            
            if (!token || !authTime) return false;
            
            const oneHour = 60 * 60 * 1000;
            const isExpired = (Date.now() - parseInt(authTime)) > oneHour;
            
            if (isExpired) {
                localStorage.removeItem('adminAuth');
                localStorage.removeItem('adminAuthTime');
                return false;
            }
            
            return true;
        }

        // Redirect if already authenticated
        if (isAuthenticated()) {
            window.location.href = '/admin/index.html';
        }

        // Google login handler
        document.getElementById('googleLoginBtn').addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<div class="loading"></div><span>Signing in...</span>';
            window.location.href = '/api/auth/google';
        });
    </script>
</body>
</html>