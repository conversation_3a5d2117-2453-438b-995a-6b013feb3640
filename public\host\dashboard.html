<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Dashboard - Wine Tasting Management</title>
    <link rel="stylesheet" href="../shared/theme.css">
    <script src="../shared/premiumFeatures.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
        

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <!-- Line 1: Subtitle + Language Switcher (will be added by JS) -->
            <div class="header-row-1">
                <div class="winery-subtitle">
                    <span class="french-text-white">Tableau des hôtes</span>
                    <span class="english-text-white">Hosts dashboard</span>
                </div>
                <div class="language-switcher"></div>
            </div>
            
            <!-- Line 2: Logo + Winery Name -->
            <div class="header-row-2">
                <div class="winery-logo" id="wineryLogo"></div>
                <h1 id="wineryName">Loading...</h1>
            </div>
            
            <!-- Line 3: Stats -->
            <div class="dashboard-stats">
                <div class="stat-item">
                    <span class="stat-number" id="activeSessions">-</span>
                    <span class="stat-label french-text-white">Groupes</span>
                    <span class="stat-label english-text-white">Groups</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalGuests">-</span>
                    <span class="stat-label french-text-white">Invités</span>
                    <span class="stat-label english-text-white">Guests</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="plateauxServed">-</span>
                    <span class="stat-label french-text-white">Plateaux</span>
                    <span class="stat-label english-text-white">Boards</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="inProgress">-</span>
                    <span class="stat-label french-text-white">Actives</span>
                    <span class="stat-label english-text-white">Active</span>
                </div>
                <div class="refresh-indicator">
                    <div class="refresh-dot"></div>
                    <span>Auto-refresh</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <h2 class="dashboard-title"></h2>
        
        <!-- Loading State -->
        <div id="loadingState" class="loading">
            <div class="loading-spinner"></div>
            <p>Loading dashboard...</p>
        </div>

        <!-- Sessions Grid -->
        <div id="sessionsGrid" class="sessions-grid" style="display: none;">
            <!-- Session tiles will be dynamically inserted here -->
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="empty-state" style="display: none;">
    <h3>
        <span class="french-text">Aucune Session Active</span>
        <span class="english-text">No Active Sessions</span>
    </h3>
    <p>
        <span class="french-text">Les nouvelles sessions de dégustation apparaîtront ici lorsque les invités créeront des commandes.</span>
        <span class="english-text">New tasting sessions will appear here as guests create orders.</span>
    </p>
</div>
    </main>

    <script>
        // =============================================
        // Language switching Version
        // =============================================

        function createLanguageSwitcher() {
            const switchButton = document.createElement('div');
            switchButton.className = 'language-switcher';
            switchButton.innerHTML = `
                <button class="lang-btn" id="fr-btn">FR</button>
                <button class="lang-btn" id="en-btn">EN</button>
            `;
            
            // Append to header instead of body
            const headerRow1 = document.querySelector('.header-row-1');
            if (headerRow1) {
                headerRow1.appendChild(switchButton);
            } else {
                document.body.appendChild(switchButton);
            }
            
            // Add click handlers with localStorage
            document.getElementById('fr-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'fr');
                document.body.setAttribute('data-lang', 'fr');
                document.getElementById('fr-btn').classList.add('active');
                document.getElementById('en-btn').classList.remove('active');
                console.log('French mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
            };
            
            document.getElementById('en-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'en');
                document.body.setAttribute('data-lang', 'en');
                document.getElementById('en-btn').classList.add('active');
                document.getElementById('fr-btn').classList.remove('active');
                console.log('English mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
            };
            
            // Load saved preference or default to French
            const savedLang = localStorage.getItem('selectedLanguage') || 'fr';
            document.getElementById(savedLang + '-btn').click();
        }

        // Run when page loads
        document.addEventListener('DOMContentLoaded', createLanguageSwitcher);

        // =============================================


        class HostDashboard {
            constructor() {
                this.refreshInterval = null;
                this.lastUpdateTime = null;
                this.initializeDashboard();
            }

            async initializeDashboard() {
                try {
                    await this.loadDashboardData();
                    this.startAutoRefresh();
                } catch (error) {
                    console.error('Dashboard initialization failed:', error);
                    this.showError('Failed to load dashboard');
                }
            }

            async loadDashboardData() {
                try {
                    const response = await fetch('/api/host/dashboard');
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    
                    if (!data.success) {
                        throw new Error(data.error || 'Failed to load dashboard data');
                    }

                    // Check if operating hours are closed
                    if (!data.isOpen) {
                        this.showClosedDashboard(data);
                        this.stopAutoRefresh();
                        this.updateRefreshIndicator('Offline');
                        return;
                    }

                    // Dashboard is open - resume auto-refresh if stopped
                    if (!this.refreshInterval) {
                        this.startAutoRefresh();
                    }

                    this.updateDashboard(data);
                    this.updateRefreshIndicator('Auto-refresh');
                    
                } catch (error) {
                    console.error('Error loading dashboard:', error);
                    throw error;
                }
            }

            updateDashboard(data) {
                // Update header info
                document.getElementById('wineryName').textContent = data.winery.name;
                
                // Show logo only for premium wineries, otherwise show nothing
                const logoElement = document.getElementById('wineryLogo');
                if (PremiumFeatures.hasFeature(data.winery, 'logo_display') && data.winery.logo) {
                    logoElement.innerHTML = `<img src="${data.winery.logo}" alt="${data.winery.name}">`;
                    logoElement.style.display = 'block';
                    document.querySelector('.header-row-2 h1').style.marginLeft = '0px';
                } else {
                    logoElement.style.display = 'none';
                    document.querySelector('.header-row-2 h1').style.marginLeft = '10px';
                }
                document.getElementById('activeSessions').textContent = data.summary.totalActiveSessions;
                document.getElementById('totalGuests').textContent = data.summary.totalActiveGuests;
                document.getElementById('plateauxServed').textContent = data.summary.plateauxServed;
                document.getElementById('inProgress').textContent = data.summary.sessionsInProgress;

                // Update sessions grid
                this.renderSessions(data.sessions);

                // Hide loading, show content
                document.getElementById('loadingState').style.display = 'none';
                
                if (data.sessions.length === 0) {
                    document.getElementById('sessionsGrid').style.display = 'none';
                    document.getElementById('emptyState').style.display = 'block';
                } else {
                    document.getElementById('sessionsGrid').style.display = 'grid';
                    document.getElementById('emptyState').style.display = 'none';
                }

                this.lastUpdateTime = new Date();
            }

            showClosedDashboard(data) {
                // Update header with winery name
                document.getElementById('wineryName').textContent = data.winery.name;
                
                // Show logo only for premium wineries, otherwise show nothing
                const logoElement = document.getElementById('wineryLogo');
                if (PremiumFeatures.hasFeature(data.winery, 'logo_display') && data.winery.logo) {
                    logoElement.innerHTML = `<img src="${data.winery.logo}" alt="${data.winery.name}">`;
                    logoElement.style.display = 'block';
                    document.querySelector('.header-row-2 h1').style.marginLeft = '0px';
                } else {
                    logoElement.style.display = 'none';
                    document.querySelector('.header-row-2 h1').style.marginLeft = '10px';
                }
                
                // Set all stats to 0
                document.getElementById('activeSessions').textContent = '0';
                document.getElementById('totalGuests').textContent = '0';
                document.getElementById('plateauxServed').textContent = '0';
                document.getElementById('inProgress').textContent = '0';
                
                // Hide loading and sessions grid
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('sessionsGrid').style.display = 'none';
                
                // Show empty state (will use existing message)
                document.getElementById('emptyState').style.display = 'block';
            }

            updateRefreshIndicator(status) {
                const refreshIndicator = document.querySelector('.refresh-indicator span');
                if (refreshIndicator) {
                    refreshIndicator.textContent = status;
                }
                
                const refreshDot = document.querySelector('.refresh-dot');
                if (refreshDot) {
                    if (status === 'Offline') {
                        refreshDot.style.backgroundColor = '#dc3545';
                    } else {
                        refreshDot.style.backgroundColor = '#28a745';
                    }
                }
            }

            renderSessions(sessions) {
                const grid = document.getElementById('sessionsGrid');
                grid.innerHTML = '';

                sessions.forEach(session => {
                    const tile = this.createSessionTile(session);
                    grid.appendChild(tile);
                });
            }

            createSessionTile(session) {
                const tile = document.createElement('div');
                tile.className = 'session-tile';

                let iconFile = 'wine-full.png';
                let progressText = `${session.wineOrders.served}/${session.wineOrders.total}`;
                let progressBar = session.wineOrders.progress || 0;
                
                if (session.plateauOrders.total > 0) {
                    iconFile = 'plateau.png';
                    const totalOrders = session.wineOrders.total + session.plateauOrders.total;
                    const servedOrders = session.wineOrders.served + session.plateauOrders.served;
                    progressText = `${servedOrders}/${totalOrders}`;
                    progressBar = totalOrders > 0 ? Math.round((servedOrders / totalOrders) * 100) : 0;
                }

                if (session.overallStatus === 'completed') {
                    iconFile = 'wine-empty.png';
                    progressText = '<span class="french-text">TERMINÉ</span><span class="english-text">COMPLETED</span>';
                    progressBar = 100;
                }

                // PROTOTYPE MATCH: Real timestamp like "3:00 PM"
                const timestamp = new Date(session.createdAt).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });

                tile.innerHTML = `
                    <div class="tile-header">
                        <div class="group-info">
                            <h3>${this.escapeHtml(session.groupName)}</h3>
                            <div class="group-meta">
                                <span class="party-size french-text-white">${session.guestCount} ${session.guestCount === 1 ? 'invité' : 'invités'}</span>
                                <span class="party-size english-text-white">${session.guestCount} ${session.guestCount === 1 ? 'guest' : 'guests'}</span>
                                <span class="time-info">${timestamp}</span>
                            </div>
                        </div>
                    </div>

                    <div class="service-section">
                        <div class="service-icon">
                            <img src="../shared/assets/${iconFile}" alt="Service Status">
                        </div>
                        <div class="service-progress">${progressText}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressBar}%"></div>
                        </div>
                    </div>
                `;

                tile.addEventListener('click', () => this.openSessionManagement(session.sessionId));
                return tile;
            }

            formatTimeAgo(dateString) {
                const now = new Date();
                const created = new Date(dateString);
                const diffMinutes = Math.floor((now - created) / (1000 * 60));

                if (diffMinutes < 1) return 'Just now';
                if (diffMinutes < 60) return `${diffMinutes}m ago`;
                
                const diffHours = Math.floor(diffMinutes / 60);
                if (diffHours < 24) return `${diffHours}h ago`;
                
                return created.toLocaleDateString();
            }

            openSessionManagement(sessionId) {
                window.location.href = `session.html?id=${sessionId}`;
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.loadDashboardData().catch(error => {
                        console.error('Auto-refresh failed:', error);
                    });
                }, 30000);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }

            showError(message) {
                document.getElementById('loadingState').innerHTML = `
                    <div style="color: #ff6b6b;">
                        <h3>Error</h3>
                        <p>${this.escapeHtml(message)}</p>
                        <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            Retry
                        </button>
                    </div>
                `;
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            getWineryInitials(name) {
                return name.split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
            }
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new HostDashboard();
        });

        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            if (window.hostDashboard) {
                window.hostDashboard.stopAutoRefresh();
            }
        });
    </script>
</body>
</html>
