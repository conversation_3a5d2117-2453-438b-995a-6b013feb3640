/* =============================================
   WINE TASTING MANAGEMENT - MASTER STYLESHEET
   Single source of truth for all styling
   ============================================= */

/* =============================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ============================================= */

:root {
  /* Wine Industry Color Palette */
  --wine-deep-red: #b0aeae;
  --wine-burgundy: #75192b;
  --wine-gold: #f5ecd6;
  --wine-champagne: #F7E7CE;
  --wine-purple: #6F4E7C;
  
  /* Text Colors - Bilingual System */
  --french-text: #000000;
  --english-text: #DC143C;
  --neutral-text: #333333;
  --light-text: #666666;
  --white-text: #FFFFFF;
  
  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9F9F9;
  --bg-accent: #FDF6E3;
  --bg-dark: #2C1810;
  
  /* Status Colors */
  --success-green: #228B22;
  --warning-orange: #FF8C00;
  --error-red: #DC143C;
  --info-blue: #4682B4;
  
  /* Winery Customizable Colors (overrideable) */
  --winery-primary: var(--wine-deep-red);
  --winery-secondary: var(--wine-gold);
  --winery-accent: var(--wine-champagne);
  
  /* Typography */
  --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-heading: 'Georgia', 'Times New Roman', serif;
  --font-mono: 'Courier New', monospace;
  
  /* Font Sizes - Mobile First */
  --text-xs: 0.75rem;   /* 12px */
  --text-sm: 0.875rem;  /* 14px */
  --text-base: 1rem;    /* 16px */
  --text-lg: 1.125rem;  /* 18px */
  --text-xl: 1.25rem;   /* 20px */
  --text-2xl: 1.5rem;   /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem;  /* 36px */
  
  /* Spacing System */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* =============================================
   RESET & BASE STYLES
   ============================================= */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--neutral-text);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* =============================================
   TYPOGRAPHY SYSTEM
   ============================================= */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.3;
  color: var(--winery-primary);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin-bottom: var(--space-4);
}

/* Bilingual Text Colors */
.text-french {
  color: var(--french-text);
}

.text-english {
  color: var(--english-text);
}

.text-neutral {
  color: var(--neutral-text);
}

/* =============================================
   BUTTON SYSTEM
   ============================================= */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch target size */
  gap: var(--space-2);
}

.btn:focus {
  outline: 2px solid var(--winery-primary);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--winery-primary);
  color: var(--white-text);
  border-color: var(--winery-primary);
}

.btn-primary:hover {
  background-color: var(--wine-burgundy);
  border-color: var(--wine-burgundy);
}

.btn-secondary {
  background-color: transparent;
  color: var(--winery-primary);
  border-color: var(--winery-primary);
}

.btn-secondary:hover {
  background-color: var(--winery-primary);
  color: var(--white-text);
}

.btn-success {
  background-color: var(--success-green);
  color: var(--white-text);
  border-color: var(--success-green);
}

.btn-warning {
  background-color: var(--warning-orange);
  color: var(--white-text);
  border-color: var(--warning-orange);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  min-height: 52px;
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  min-height: 36px;
}

.btn-full {
  width: 100%;
}

/* =============================================
   FORM ELEMENTS
   ============================================= */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--neutral-text);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  border: 1px solid #D1D5DB;
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  transition: border-color var(--transition-fast);
  min-height: 44px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--winery-primary);
  box-shadow: 0 0 0 3px rgba(139, 0, 0, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-error {
  color: var(--error-red);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* =============================================
   CARD SYSTEM
   ============================================= */

.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid #E5E7EB;
  background-color: var(--bg-secondary);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid #E5E7EB;
  background-color: var(--bg-secondary);
}

/* =============================================
   TOGGLE SWITCHES (HOST DASHBOARD)
   ============================================= */

.toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: var(--transition-normal);
  border-radius: var(--radius-full);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: var(--transition-normal);
  border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
  background-color: var(--success-green);
}

.toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* =============================================
   STATUS INDICATORS
   ============================================= */

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-sm);
  font-weight: 500;
  border-radius: var(--radius-full);
  gap: var(--space-1);
}

.status-ordered {
  background-color: #FEF3C7;
  color: #92400E;
}

.status-served {
  background-color: #D1FAE5;
  color: #065F46;
}

.status-cancelled {
  background-color: #FEE2E2;
  color: #991B1B;
}

/* =============================================
   LAYOUT UTILITIES
   ============================================= */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-lg {
  max-width: 1400px;
}

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

/* =============================================
   WINE SPECIFIC COMPONENTS
   ============================================= */

.wine-card {
  border: 1px solid #E5E7EB;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.wine-card:hover {
  border-color: var(--winery-primary);
  box-shadow: var(--shadow-md);
}

.wine-card.selected {
  border-color: var(--winery-primary);
  background-color: var(--winery-accent);
}

.wine-category {
  display: inline-block;
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-2);
}

.wine-category-red {
  background-color: #FEE2E2;
  color: #991B1B;
}

.wine-category-white {
  background-color: #FEF7CD;
  color: #92400E;
}

.wine-category-rose {
  background-color: #FCE7F3;
  color: #BE185D;
}

.wine-category-sparkling {
  background-color: #EEF2FF;
  color: #3730A3;
}

/* =============================================
   RESPONSIVE DESIGN
   ============================================= */

/* Mobile First - Base styles above are mobile */

/* Tablet */
@media (min-width: 768px) {
  :root {
    --text-xs: 0.8rem;
    --text-sm: 0.9rem;
    --text-base: 1rem;
    --text-lg: 1.2rem;
    --text-xl: 1.4rem;
    --text-2xl: 1.6rem;
    --text-3xl: 2rem;
    --text-4xl: 2.5rem;
  }

  .container {
    padding: 0 var(--space-6);
  }

  .grid-tablet-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }

  .grid-desktop-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-desktop-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* =============================================
   HOST DASHBOARD - VISUAL SPEED SERVICE
   ============================================= */

.service-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;
}

.service-icon {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--wine-deep-red) 0%, var(--wine-dark) 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-details {
  flex: 1;
}

.service-type {
  font-size: 18px;
  font-weight: 700;
  color: var(--wine-dark);
  margin-bottom: 8px;
}

.service-progress {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #059669 0%, #047857 100%); /* GREEN instead of yellow */
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 1px 3px rgba(5, 150, 105, 0.3);
}


/* =============================================
   DRAGGABLE SLIDING TOGGLES - DELIBERATE ACTION
   ============================================= */

.serving-toggle {
  position: relative;
  width: 170px;
  height: 36px;
  background: #dc2626;
  border: 2px solid #b91c1c;
  border-radius: 18px;
  cursor: grab;
  margin-left: 15px;
  user-select: none;
}

.serving-toggle.served {
  background: var(--success-green);
  border-color: #047857;
}

.serving-toggle .toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 30px;
  height: 30px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.serving-toggle.served .toggle-slider {
  transform: translateX(134px);
}

.serving-toggle:active .toggle-slider {
  cursor: grabbing;
}

/* =============================================
   DASHBOARD CARDS - ICON CENTERED ON CARD
   ============================================= */

.service-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;
}

.service-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--wine-deep-red) 0%, var(--wine-dark) 100%);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-icon img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-details {
  width: 100%;
}

.service-progress {
  font-size: 24px;
  color: var(--wine-dark);
  margin-bottom: 8px;
  font-weight: 700;
}



/* Hide lock status and NEW badge on dashboard cards */
.session-tile .lock-indicator,
.session-tile .status-badge {
  display: none;
}


/* =============================================
   MOUSE DRAG TOGGLES - LAPTOP COMPATIBLE
   ============================================= */

.serving-toggle {
  position: relative;
  width: 170px;
  height: 36px;
  background: #dc2626;
  border: 2px solid #b91c1c;
  border-radius: 18px;
  cursor: grab;
  margin-left: 15px;
  user-select: none;
}

.serving-toggle.served {
  background: var(--success-green);
  border-color: #047857;
}

.serving-toggle .toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 30px;
  height: 30px;
  background: white;
  border-radius: 50%;
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: transform 0.3s ease;
}

.serving-toggle:active {
  cursor: grabbing;
}

.serving-toggle:active .toggle-slider {
  cursor: grabbing;
  transition: none;
}

.serving-toggle.served .toggle-slider {
  transform: translateX(134px);
}

/* =============================================
   PRINT STYLES
   ============================================= */

@media print {
  .btn,
  .toggle,
  .no-print {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}