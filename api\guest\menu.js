// api/guest/menu.js - FIXED to detect subdomain properly

import { query } from '../../lib/database.js';

// Operating hours utility
// Operating hours utility with debug logging
function isWineryOpen(winery, extensionHours = 2) {
  try {
    const timezone = winery.timezone || 'America/Montreal';
    const now = new Date();
    
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      weekday: 'long',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(now);
    const currentDay = parts.find(part => part.type === 'weekday').value.toLowerCase();
    const currentTime = `${parts.find(part => part.type === 'hour').value}:${parts.find(part => part.type === 'minute').value}`;
    
    const dayHours = winery.operational_hours?.[currentDay];
    
    if (!dayHours || dayHours.closed) {
      console.log(`HOURS DEBUG: ${winery.name} - Closed today (${currentDay})`);
      return { isOpen: false, currentDay, currentTime, reason: 'Closed today' };
    }
    
    const currentMinutes = timeToMinutes(currentTime);
    const openMinutes = timeToMinutes(dayHours.open);
    const closeMinutes = timeToMinutes(dayHours.close);
    
    // Calculate listening window
    const listenEndMinutes = closeMinutes + (extensionHours * 60);
    
    const isOpen = currentMinutes >= openMinutes && currentMinutes <= closeMinutes;
    
    // Debug logging
    const formatMinutes = (min) => `${Math.floor(min/60).toString().padStart(2,'0')}:${(min%60).toString().padStart(2,'0')}`;
    console.log(`HOURS DEBUG: ${winery.name} on ${currentDay}`);
    console.log(`  Current time: ${currentTime} (${currentMinutes} min)`);
    console.log(`  Store hours: ${dayHours.open} - ${dayHours.close}`);
    console.log(`  Listening window: ${dayHours.open} - ${formatMinutes(listenEndMinutes)}`);
    console.log(`  Result: ${isOpen ? 'ONLINE' : 'OFFLINE'}`);
    
    return { 
      isOpen, 
      currentDay, 
      currentTime, 
      reason: isOpen ? 'Open' : 'Outside listening window',
      debug: {
        listenStart: formatMinutes(openMinutes),
        listenEnd: formatMinutes(listenEndMinutes),
        storeOpen: dayHours.open,
        storeClose: dayHours.close
      }
    };
    
  } catch (error) {
    console.log(`HOURS DEBUG: Error checking hours - ${error.message}`);
    return { isOpen: true, reason: 'Unable to determine - defaulting open' };
  }
}

function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // FIXED: Extract winery slug from subdomain
    const host = req.headers.host || '';
    const winerySlug = host.split('.')[0];
    
    console.log('Host:', host);
    console.log('Winery Slug:', winerySlug);
    
    if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
      return res.status(400).json({ 
        error: 'Invalid winery subdomain' 
      });
    }

    // Look up winery by slug
    const wineryResult = await query(
      'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
      [winerySlug]
    );

    if (!wineryResult.rows.length) {
      return res.status(404).json({ 
        error: `Winery "${winerySlug}" not found or unavailable` 
      });
    }

    const winery = wineryResult.rows[0];
    
    // Check operating hours
    const hoursCheck = isWineryOpen(winery, 2);

    if (!hoursCheck.isOpen) {
        return res.status(200).json({
            success: true,
            isOpen: false,
            winery: {
                name: winery.name,
                subscription_tier: winery.subscription_tier,
                logo: winery.logo,
                closedMessage: {
                    fr: winery.closed_message_fr || "Nous sommes actuellement fermés.",
                    en: winery.closed_message_en || "We are currently closed.",
                    wineryName: winery.name  // Add this
                }
            },
            wines: [],
            plateaux: []
        });
    }

    // Get wines for THIS winery (not hardcoded ID 1)
    const wines = await query(`
      SELECT 
          w.*,
          wc.name_fr as category_name_fr,
          wc.name_en as category_name_en,
          wc.sort_order as category_sort_order
      FROM wines w
      LEFT JOIN wine_categories wc ON w.category = wc.category_code
      WHERE w.winery_id = $1 AND w.is_available = true 
      ORDER BY w.sort_order
    `, [winery.id]);
    
    // Get plateaux for THIS winery (not hardcoded ID 1)  
    const plateaux = await query(
      'SELECT * FROM plateaux WHERE winery_id = $1 AND is_available = true ORDER BY sort_order',
      [winery.id]
    );
    
    res.status(200).json({
      success: true,
      wines: wines.rows,
      plateaux: plateaux.rows,
      winery: {
          name: winery.name,
          subscription_tier: winery.subscription_tier,
          logo: winery.logo,
          max_guests_per_group: winery.max_guests_per_group,
          wines_per_guest: winery.wines_per_guest,
          max_plateaux_per_group: winery.max_plateaux_per_group
      }
    });
  } catch (error) {
    console.error('Menu API error:', error);
    res.status(500).json({ error: 'Failed to fetch menu' });
  }
}
