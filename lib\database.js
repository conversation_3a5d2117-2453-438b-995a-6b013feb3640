// lib/database.js - MINIMAL CHANGE - Only pool settings
// SAFE: Keeps exact same query() function, only fixes connection limits

import { Pool } from 'pg';

let pool;

export const getDB = () => {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      
      // ONLY THESE LINES CHANGED - Critical connection limits
      max: 5,                    // CHANGED: was unlimited, now max 5 connections
      idleTimeoutMillis: 3000,   // CHANGED: was 1000, now 3 seconds
      
      // KEEP EVERYTHING ELSE EXACTLY THE SAME
      connectionTimeoutMillis: 5000,
      query_timeout: 15000,
      statement_timeout: 20000,
      allowExitOnIdle: true
    });
  }
  return pool;
};

// KEEP EXACT SAME QUERY FUNCTION - NO CHANGES
export const query = async (text, params = []) => {
  const pool = getDB();
  const client = await pool.connect();
  
  try {
    await client.query('SET statement_timeout = 15000');
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
};