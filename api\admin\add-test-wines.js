import { query } from '../../lib/database.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Add test wines to EZvino-Demo (winery ID 4)
    const testWines = [
      { name: 'Test Red Wine', category: 'red', description_fr: 'Vin rouge test', description_en: 'Test red wine' },
      { name: 'Test White Wine', category: 'white', description_fr: 'Vin blanc test', description_en: 'Test white wine' },
      { name: 'Test Rosé', category: 'rose', description_fr: 'Rosé test', description_en: 'Test rosé' },
      { name: 'Test Sparkling', category: 'sparkling', description_fr: 'Mousseux test', description_en: 'Test sparkling' }
    ];

    const results = [];
    for (const wine of testWines) {
      const result = await query(`
        INSERT INTO wines (winery_id, name, category, description_fr, description_en, is_available, sort_order)
        VALUES ($1, $2, $3, $4, $5, true, 0)
        RETURNING id, name
      `, [4, wine.name, wine.category, wine.description_fr, wine.description_en]);
      
      results.push(result.rows[0]);
    }

    res.status(200).json({
      success: true,
      message: 'Test wines added to EZvino-Demo',
      wines: results
    });

  } catch (error) {
    console.error('Error adding test wines:', error);
    res.status(500).json({ error: 'Failed to add test wines: ' + error.message });
  }
}