// api/admin/settings.js
// FIXED VERSION - Operating hours + closed messages support

import { query } from '../../lib/database.js';

// Operating hours utility function
function isWineryOpen(winery, extensionHours = 0) {
  try {
    const timezone = winery.timezone || 'America/Montreal';
    const now = new Date();
    
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      weekday: 'long',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(now);
    const currentDay = parts.find(part => part.type === 'weekday').value.toLowerCase();
    const currentTime = `${parts.find(part => part.type === 'hour').value}:${parts.find(part => part.type === 'minute').value}`;
    
    const dayHours = winery.operational_hours?.[currentDay];
    
    if (!dayHours || dayHours.closed) {
      return { isOpen: false, currentDay, currentTime, reason: 'Closed today' };
    }
    
    const currentMinutes = timeToMinutes(currentTime);
    const openMinutes = timeToMinutes(dayHours.open);
    const closeMinutes = timeToMinutes(dayHours.close) + (extensionHours * 60);
    
    const isOpen = currentMinutes >= openMinutes && currentMinutes <= closeMinutes;
    
    return { isOpen, currentDay, currentTime, reason: isOpen ? 'Open' : 'Outside hours' };
    
  } catch (error) {
    return { isOpen: true, reason: 'Unable to determine - defaulting open' };
  }
}

function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

export default async function handler(req, res) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    // CHECK HOURS endpoint - FIRST, before main GET
    if (req.query.action === 'check-hours') {
      const { winery: wineryParam } = req.query;

      // Auto-detect winery from subdomain if not specified
      let wineryResult;
      if (wineryParam === 'auto') {
        const winerySlug = req.headers['x-winery-slug'] || 'chateau-test';
        wineryResult = await query('SELECT * FROM wineries WHERE slug = $1 AND is_active = true', [winerySlug]);
      } else {
        wineryResult = await query('SELECT * FROM wineries WHERE id = $1', [wineryParam]);
      }

      if (!wineryResult.rows.length) {
        return res.status(404).json({ error: 'Winery not found' });
      }
      
      const winery = wineryResult.rows[0];
      const hoursCheck = isWineryOpen(winery, 2); // +2 extension for host
      
      // If closed, return dashboard format with empty data
      if (!hoursCheck.isOpen) {
        return res.status(200).json({
          success: true,
          isOpen: false,
          winery: { name: winery.name },
          summary: {
            totalActiveSessions: 0,
            totalActiveGuests: 0,
            sessionsInProgress: 0
          },
          sessions: [],
          reason: hoursCheck.reason
        });
      }

      // If open, get actual session data
      const today = new Date().toISOString().split('T')[0];
      
      const sessionsResult = await query(`
        SELECT 
          ts.id as session_id,
          ts.group_name,
          ts.guest_count,
          ts.created_at,
          ts.status
        FROM tasting_sessions ts
        WHERE ts.winery_id = $1 
          AND ts.status = 'active'
          AND DATE(ts.created_at AT TIME ZONE $2) = $3
        ORDER BY ts.created_at DESC
      `, [winery.id, winery.timezone || 'America/Montreal', today]);

      const summary = {
        totalActiveSessions: sessionsResult.rows.length,
        totalActiveGuests: sessionsResult.rows.reduce((sum, session) => sum + session.guest_count, 0),
        sessionsInProgress: sessionsResult.rows.length // Simplified for now
      };

      return res.status(200).json({
        success: true,
        isOpen: true,
        winery: { name: winery.name },
        summary,
        sessions: sessionsResult.rows,
        reason: hoursCheck.reason
      });
    }

    // GET - List all wineries with operating hours and closed messages
    if (req.method === 'GET') {
      const queryText = `
          SELECT 
            w.id, w.name, w.slug, w.winery_code, w.subscription_tier, w.logo, w.email, w.phone, w.address,
            w.max_guests_per_group, w.min_guests_per_group, w.wines_per_guest, w.max_plateaux_per_group,
            w.is_active, w.created_at, w.operational_hours, w.timezone, w.closed_message_fr, w.closed_message_en,
            0 as wine_count, 0 as category_count, 0 as plateau_count, 0 as session_count, 0 as today_sessions
          FROM wineries w
          WHERE 1=1
          ORDER BY w.created_at DESC
        `;

      console.log('Settings query about to execute:', queryText);
      const result = await query(queryText);
      console.log('Settings query result rows:', result.rows.length);

      return res.status(200).json({
        success: true,
        wineries: result.rows
      });
    }

    // POST - Create new winery
    if (req.method === 'POST') {
      const {
        name,
        slug,
        email,
        phone,
        address,
        subscription_tier,
        max_guests_per_group,
        min_guests_per_group,
        wines_per_guest,
        max_plateaux_per_group,
        operational_hours,
        timezone,
        closed_message_fr,
        closed_message_en
      } = req.body;

      if (!name || !slug) {
        return res.status(400).json({
          success: false,
          error: 'Name and slug are required'
        });
      }

      const insertQuery = `
        INSERT INTO wineries (
          name, slug, email, phone, address, subscription_tier,
          max_guests_per_group, min_guests_per_group, wines_per_guest, max_plateaux_per_group,
          operational_hours, timezone, closed_message_fr, closed_message_en, logo, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, true)
        RETURNING *
      `;

      const values = [
        name.trim(),
        slug.trim(),
        email?.trim() || null,
        phone?.trim() || null,
        address?.trim() || null,
        subscription_tier || 'freemium',
        parseInt(max_guests_per_group) || 8,
        parseInt(min_guests_per_group) || 1,
        parseInt(wines_per_guest) || 4,
        parseInt(max_plateaux_per_group) || 2,
        operational_hours || '{}',
        timezone || 'America/Montreal',
        closed_message_fr?.trim() || null,
        closed_message_en?.trim() || null,
        logo?.trim() || null
      ];

      const result = await query(insertQuery, values);

      return res.status(201).json({
        success: true,
        winery: result.rows[0],
        message: 'Winery created successfully'
      });
    }

    // PATCH - Update existing winery
    if (req.method === 'PATCH') {
      const { id: wineryId } = req.query;
      
      if (!wineryId) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID is required'
        });
      }

      const {
        name,
        slug,
        email,
        phone,
        address,
        subscription_tier,
        max_guests_per_group,
        min_guests_per_group,
        wines_per_guest,
        max_plateaux_per_group,
        operational_hours,
        timezone,
        closed_message_fr,
        closed_message_en,
        logo
      } = req.body;

      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      if (name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(name.trim());
      }
      if (slug !== undefined) {
        updateFields.push(`slug = $${paramIndex++}`);
        updateValues.push(slug.trim());
      }
      if (email !== undefined) {
        updateFields.push(`email = $${paramIndex++}`);
        updateValues.push(email?.trim() || null);
      }
      if (phone !== undefined) {
        updateFields.push(`phone = $${paramIndex++}`);
        updateValues.push(phone?.trim() || null);
      }
      if (address !== undefined) {
        updateFields.push(`address = $${paramIndex++}`);
        updateValues.push(address?.trim() || null);
      }
      if (subscription_tier !== undefined) {
        updateFields.push(`subscription_tier = $${paramIndex++}`);
        updateValues.push(subscription_tier);
      }
      if (max_guests_per_group !== undefined) {
        updateFields.push(`max_guests_per_group = $${paramIndex++}`);
        updateValues.push(parseInt(max_guests_per_group));
      }
      if (min_guests_per_group !== undefined) {
        updateFields.push(`min_guests_per_group = $${paramIndex++}`);
        updateValues.push(parseInt(min_guests_per_group));
      }
      if (wines_per_guest !== undefined) {
        updateFields.push(`wines_per_guest = $${paramIndex++}`);
        updateValues.push(parseInt(wines_per_guest));
      }
      if (max_plateaux_per_group !== undefined) {
        updateFields.push(`max_plateaux_per_group = $${paramIndex++}`);
        updateValues.push(parseInt(max_plateaux_per_group));
      }
      if (operational_hours !== undefined) {
        updateFields.push(`operational_hours = $${paramIndex++}::jsonb`);
        updateValues.push(operational_hours);
      }
      if (timezone !== undefined) {
        updateFields.push(`timezone = $${paramIndex++}`);
        updateValues.push(timezone);
      }
      if (closed_message_fr !== undefined) {
        updateFields.push(`closed_message_fr = $${paramIndex++}`);
        updateValues.push(closed_message_fr?.trim() || null);
      }
      if (closed_message_en !== undefined) {
        updateFields.push(`closed_message_en = $${paramIndex++}`);
        updateValues.push(closed_message_en?.trim() || null);
      }

      if (logo !== undefined) {
        updateFields.push(`logo = $${paramIndex++}`);
        updateValues.push(logo);
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No fields to update'
        });
      }

      updateValues.push(wineryId);

      const updateQuery = `
        UPDATE wineries 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;

      const result = await query(updateQuery, updateValues);

      if (!result.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Winery not found'
        });
      }

      return res.status(200).json({
        success: true,
        winery: result.rows[0],
        message: 'Winery updated successfully'
      });
    }

    // DELETE - Remove winery (soft delete)
    if (req.method === 'DELETE') {
      const { id: wineryId } = req.query;
      
      if (!wineryId) {
        return res.status(400).json({
          success: false,
          error: 'Winery ID is required'
        });
      }

      const deleteQuery = `
        UPDATE wineries 
        SET is_active = false
        WHERE id = $1
        RETURNING *
      `;

      const result = await query(deleteQuery, [wineryId]);

      if (!result.rows.length) {
        return res.status(404).json({
          success: false,
          error: 'Winery not found'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Winery deleted successfully'
      });
    }

    // PUT - Toggle winery status
    if (req.method === 'PUT') {
      const { action, id } = req.query;
      
      if (action === 'toggle' && id) {
        const toggleQuery = `
          UPDATE wineries 
          SET is_active = NOT is_active
          WHERE id = $1
          RETURNING *
        `;

        const result = await query(toggleQuery, [id]);

        if (!result.rows.length) {
          return res.status(404).json({
            success: false,
            error: 'Winery not found'
          });
        }

        return res.status(200).json({
          success: true,
          winery: result.rows[0],
          message: `Winery ${result.rows[0].is_active ? 'enabled' : 'disabled'}`
        });
      }
    }

    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });

  } catch (error) {
    console.error('Settings error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}