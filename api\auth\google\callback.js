// Handle Google OAuth callback
export default async function handler(req, res) {
  console.log('CALLBACK HIT:', req.query);
  console.log('HOST:', req.headers.host);
  console.log('ORIGIN:', req.headers.origin);
  console.log('CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? 'exists' : 'missing');
  console.log('CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'exists' : 'missing');

  const { code, error } = req.query;

  if (error) {
    return res.redirect('/admin/auth.html?error=access_denied');
  }

  if (!code) {
    return res.redirect('/admin/auth.html?error=no_code');
  }

  try {
    // Exchange code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `https://${req.headers.host}/api/auth/google/callback`
      })
    });

    const tokens = await tokenResponse.json();
    console.log('Google tokens:', tokens.error || 'success');
    
    if (!tokens.access_token) {
      throw new Error('No access token received');
    }

    // Get user info
    const userResponse = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokens.access_token}`);
    const user = await userResponse.json();

    // Check if user is authorized (Jeff or May)
    const authorizedEmails = ['<EMAIL>', '<EMAIL>'];
    
    if (!authorizedEmails.includes(user.email)) {
      return res.redirect('/admin/auth.html?error=unauthorized');
    }

    // Create admin token (same format as your current system)
    const adminToken = Buffer.from(`${user.email}:${Date.now()}:admin`).toString('base64');
    
    // Redirect with token
    // Replace the final redirect line with:
    return res.status(302).setHeader('Location', `/admin/auth.html?token=${adminToken}&name=${encodeURIComponent(user.name)}`).end();

  } catch (error) {
    console.error('OAuth callback error:', error);
    res.redirect('/admin/auth.html?error=server_error');
  }
}