// =============================================
// CENTRALIZED API CLIENT
// Single source for all API communications
// =============================================

class WineTastingAPI {
    constructor() {
        this.baseURL = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    // Generic request handler with error handling
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            console.log(`API Request: ${config.method || 'GET'} ${url}`);
            
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || data.error || `HTTP ${response.status}`);
            }

            console.log(`API Response: ${url}`, data);
            return data;

        } catch (error) {
            console.error(`API Error: ${url}`, error);
            throw error;
        }
    }

    // =============================================
    // GUEST API METHODS
    // =============================================

    // Get winery menu (wines and plateaux)
    async getMenu() {
        return this.request('/guest/menu');
    }

    // Create new tasting session
    async createSession(sessionData) {
        return this.request('/guest/session', {
            method: 'POST',
            body: JSON.stringify(sessionData)
        });
    }

    // Get session status by group code
    async getSession(groupCode) {
        return this.request(`/guest/session/${groupCode}`);
    }

    // Submit guest email for recap (premium feature)
    async submitGuestEmail(sessionId, emailData) {
        return this.request('/guest/email', {
            method: 'POST',
            body: JSON.stringify({
                session_id: sessionId,
                ...emailData
            })
        });
    }

    // =============================================
    // HOST API METHODS
    // =============================================

    // Get host dashboard data
    async getHostDashboard() {
        return this.request('/host/dashboard');
    }

    // Get detailed session for host management
    async getHostSession(sessionId) {
        return this.request(`/host/session/${sessionId}`);
    }

    // Update wine serving status
    async updateWineStatus(wineOrderId, status, servedBy = '') {
        return this.request('/host/wine-status', {
            method: 'PATCH',
            body: JSON.stringify({
                wine_order_id: wineOrderId,
                status: status,
                served_by: servedBy
            })
        });
    }

    // Update plateau serving status
    async updatePlateauStatus(plateauOrderId, status, servedBy = '') {
        return this.request('/host/plateau-status', {
            method: 'PATCH',
            body: JSON.stringify({
                plateau_order_id: plateauOrderId,
                status: status,
                served_by: servedBy
            })
        });
    }

    // Lock or unlock session for guest modifications
    async toggleSessionLock(sessionId, isLocked, lockedBy = '') {
        return this.request('/host/session-lock', {
            method: 'PATCH',
            body: JSON.stringify({
                session_id: sessionId,
                is_locked: isLocked,
                locked_by: lockedBy
            })
        });
    }

    // =============================================
    // ADMIN API METHODS
    // =============================================

    // Get all wines for winery
    async getWines() {
        return this.request('/admin/wines');
    }

    // Add new wine
    async addWine(wineData) {
        return this.request('/admin/wines', {
            method: 'POST',
            body: JSON.stringify(wineData)
        });
    }

    // Update wine
    async updateWine(wineId, wineData) {
        return this.request(`/admin/wines/${wineId}`, {
            method: 'PATCH',
            body: JSON.stringify(wineData)
        });
    }

    // Delete wine (soft delete - set is_available = false)
    async deleteWine(wineId) {
        return this.request(`/admin/wines/${wineId}`, {
            method: 'DELETE'
        });
    }

    // Get winery settings
    async getWinerySettings() {
        return this.request('/admin/settings');
    }

    // Update winery settings
    async updateWinerySettings(settings) {
        return this.request('/admin/settings', {
            method: 'PATCH',
            body: JSON.stringify(settings)
        });
    }

    // =============================================
    // ANALYTICS API METHODS (Premium)
    // =============================================

    // Get analytics dashboard data
    async getAnalyticsDashboard(dateRange = {}) {
        const params = new URLSearchParams(dateRange);
        return this.request(`/admin/analytics?${params}`);
    }

    // Export analytics data
    async exportAnalytics(format = 'csv', dateRange = {}) {
        const params = new URLSearchParams({ format, ...dateRange });
        return this.request(`/admin/analytics/export?${params}`);
    }

    // =============================================
    // UTILITY METHODS
    // =============================================

    // Health check
    async healthCheck() {
        return this.request('/health', { baseURL: '' }); // Health is at root level
    }

    // Handle file uploads (for wine images, etc.)
    async uploadFile(file, type = 'wine') {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);

        return this.request('/admin/upload', {
            method: 'POST',
            headers: {}, // Let browser set Content-Type for FormData
            body: formData
        });
    }

    // =============================================
    // REAL-TIME UPDATES
    // =============================================

    // Poll for session updates (used by host dashboard and guest session page)
    startPolling(endpoint, callback, interval = 30000) {
        const poll = async () => {
            try {
                const data = await this.request(endpoint);
                callback(data);
            } catch (error) {
                console.error('Polling error:', error);
                // Continue polling even on errors
            }
        };

        // Initial call
        poll();

        // Set up interval
        const intervalId = setInterval(poll, interval);

        // Return cleanup function
        return () => clearInterval(intervalId);
    }

    // =============================================
    // ERROR HANDLING HELPERS
    // =============================================

    // Handle common API errors
    handleError(error, context = '') {
        console.error(`${context} error:`, error);

        // Common error scenarios
        if (error.message.includes('not found')) {
            return 'The requested item was not found.';
        }
        
        if (error.message.includes('unauthorized')) {
            return 'You are not authorized to perform this action.';
        }
        
        if (error.message.includes('validation')) {
            return 'Please check your input and try again.';
        }
        
        if (error.message.includes('network') || error.message.includes('fetch')) {
            return 'Network error. Please check your connection and try again.';
        }

        // Return original error message for specific errors
        return error.message || 'An unexpected error occurred.';
    }

    // Show user-friendly error messages
    showError(message, containerId = 'error-message') {
        const errorContainer = document.getElementById(containerId);
        if (errorContainer) {
            const errorText = errorContainer.querySelector('.text-error') || 
                             errorContainer.querySelector('p') ||
                             errorContainer;
            
            errorText.textContent = message;
            errorContainer.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                errorContainer.style.display = 'none';
            }, 5000);
            
            // Scroll to error
            errorContainer.scrollIntoView({ behavior: 'smooth' });
        } else {
            // Fallback to alert if no error container
            alert(message);
        }
    }

    // Show success messages
    showSuccess(message, containerId = 'success-message') {
        const successContainer = document.getElementById(containerId);
        if (successContainer) {
            const successText = successContainer.querySelector('.text-success') || 
                               successContainer.querySelector('p') ||
                               successContainer;
            
            successText.textContent = message;
            successContainer.style.display = 'block';
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                successContainer.style.display = 'none';
            }, 3000);
        }
    }
}

// =============================================
// GLOBAL API INSTANCE
// =============================================

// Create global API instance
window.wineAPI = new WineTastingAPI();

// =============================================
// CONVENIENCE FUNCTIONS
// =============================================

// Convenience functions for backward compatibility
window.api = {
    // Guest functions
    getMenu: () => window.wineAPI.getMenu(),
    createSession: (data) => window.wineAPI.createSession(data),
    getSession: (code) => window.wineAPI.getSession(code),
    
    // Host functions
    getDashboard: () => window.wineAPI.getHostDashboard(),
    getHostSession: (id) => window.wineAPI.getHostSession(id),
    updateWineStatus: (id, status, by) => window.wineAPI.updateWineStatus(id, status, by),
    updatePlateauStatus: (id, status, by) => window.wineAPI.updatePlateauStatus(id, status, by),
    
    // Admin functions
    getWines: () => window.wineAPI.getWines(),
    addWine: (data) => window.wineAPI.addWine(data),
    updateWine: (id, data) => window.wineAPI.updateWine(id, data),
    deleteWine: (id) => window.wineAPI.deleteWine(id),
    
    // Utility functions
    health: () => window.wineAPI.healthCheck(),
    startPolling: (endpoint, callback, interval) => window.wineAPI.startPolling(endpoint, callback, interval),
    showError: (message) => window.wineAPI.showError(message),
    showSuccess: (message) => window.wineAPI.showSuccess(message)
};

// =============================================
// WINERY DETECTION HELPER
// =============================================

// Detect current winery from URL/domain
window.getCurrentWinery = () => {
    const hostname = window.location.hostname;
    const subdomain = hostname.split('.')[0];
    
    // For development
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
        return 'chateau-test'; // Default test winery
    }
    
    // For production subdomains
    return subdomain;
};

// =============================================
// FORM VALIDATION HELPERS
// =============================================

window.validateForm = {
    // Validate required fields
    required: (value, fieldName) => {
        if (!value || value.trim().length === 0) {
            throw new Error(`${fieldName} is required`);
        }
        return value.trim();
    },
    
    // Validate email format
    email: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Please enter a valid email address');
        }
        return email;
    },
    
    // Validate number range
    numberRange: (value, min, max, fieldName) => {
        const num = parseInt(value);
        if (isNaN(num) || num < min || num > max) {
            throw new Error(`${fieldName} must be between ${min} and ${max}`);
        }
        return num;
    },
    
    // Validate wine selection count
    wineCount: (selectedWines, requiredCount, guestName) => {
        if (selectedWines.length !== requiredCount) {
            throw new Error(`${guestName} must select exactly ${requiredCount} wines`);
        }
        return true;
    }
};

// =============================================
// LOCAL STORAGE HELPERS (FOR DRAFT ORDERS)
// =============================================

window.orderDraft = {
    // Save order draft to localStorage
    save: (orderData) => {
        try {
            localStorage.setItem('wine_order_draft', JSON.stringify({
                ...orderData,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.warn('Could not save order draft:', error);
        }
    },
    
    // Load order draft from localStorage
    load: () => {
        try {
            const draft = localStorage.getItem('wine_order_draft');
            if (draft) {
                const data = JSON.parse(draft);
                // Only return drafts less than 1 hour old
                if (Date.now() - data.timestamp < 3600000) {
                    return data;
                }
            }
        } catch (error) {
            console.warn('Could not load order draft:', error);
        }
        return null;
    },
    
    // Clear order draft
    clear: () => {
        try {
            localStorage.removeItem('wine_order_draft');
        } catch (error) {
            console.warn('Could not clear order draft:', error);
        }
    }
};

console.log('Wine Tasting API loaded successfully');

// =============================================
// EMAIL SESSION FUNCTIONALITY (mailto: POC)
// =============================================

function emailSession() {
    const isFrench = document.body.getAttribute('data-lang') === 'fr' || 
                     document.querySelector('.french-text:not([style*="display: none"]') !== null;
    
    const wineryName = 'Winery'; // Will extract from page dynamically
    const groupName = document.querySelector('.session-group-name')?.textContent?.trim() || 'Group';
    const guestCount = document.querySelector('.session-details')?.textContent?.match(/\d+/)?.[0] || '0';
    
    const sessionDate = new Date().toLocaleDateString(isFrench ? 'fr-CA' : 'en-CA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // Collect wines by guest
    let winesList = '';
    document.querySelectorAll('.guest-wine-card').forEach(guestCard => {
        const guestName = guestCard.querySelector('.guest-name')?.textContent?.trim() || 'Guest';
        const wines = Array.from(guestCard.querySelectorAll('.wine-order-item')).map(item => {
            const wineName = item.querySelector('.wine-item-name')?.textContent?.trim() || 'Wine';
            const wineCategory = item.querySelector('.wine-item-category')?.textContent?.trim() || '';
            return `  - ${wineName}${wineCategory ? ' (' + wineCategory + ')' : ''}`;
        });
        
        if (wines.length > 0) {
            winesList += `${guestName}:\n${wines.join('\n')}\n\n`;
        }
    });
    
    // Collect plateaux
    let plateauList = '';
    document.querySelectorAll('.plateau-item').forEach((plateau, index) => {
        const plateauName = plateau.querySelector('.plateau-name')?.textContent?.trim() || `Plateau ${index + 1}`;
        const plateauQty = plateau.querySelector('.plateau-quantity')?.textContent?.match(/\d+/)?.[0] || '1';
        plateauList += `${index + 1}. ${plateauName} (${plateauQty}x)\n`;
    });
    
    let subject, body;
    
    if (isFrench) {
        subject = `Ma dégustation chez ${wineryName}`;
        body = `Bonjour,

Voici un récapitulatif de ma dégustation de vins chez ${wineryName}.

📅 Date: ${sessionDate}
👥 Groupe: ${groupName} (${guestCount} personnes)

🍷 Vins dégustés:
${winesList || 'Aucun vin sélectionné'}

${plateauList ? `🧀 Plateaux:
${plateauList}` : ''}

Merci pour cette belle expérience!

---
Propulsé par ezvino.app
`;
    } else {
        subject = `My Wine Tasting at ${wineryName}`;
        body = `Hello,

Here's a recap of my wine tasting experience at ${wineryName}.

📅 Date: ${sessionDate}
👥 Group: ${groupName} (${guestCount} people)

🍷 Wines Tasted:
${winesList || 'No wines selected'}

${plateauList ? `🧀 Plateaux:
${plateauList}` : ''}

Thank you for the wonderful experience!

---
Powered by ezvino.app
`;
    }
    
    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;
    
    showEmailConfirmation(isFrench);
}

function showEmailConfirmation(isFrench) {
    const message = isFrench 
        ? '✉️ Votre application de courriel devrait s\'ouvrir. N\'oubliez pas d\'envoyer!' 
        : '✉️ Your email app should open. Don\'t forget to send!';
    
    const notification = document.createElement('div');
    notification.className = 'email-notification';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}