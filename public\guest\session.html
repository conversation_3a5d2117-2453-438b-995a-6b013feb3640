<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session de Dégustation</title>
    <link rel="stylesheet" href="../shared/theme.css">
    <script src="../shared/premiumFeatures.js"></script>
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="EZvino" />
    <link rel="manifest" href="/site.webmanifest" />
    <style>
      

    </style>
</head>
<body>
    <div class="app-container">
        <header class="header-confirmation" id="sessionHeader">
            <div class="header-icon"></div>
            <h1 class="header-title" id="headerTitle">
                <span class="french-text">Sélection confirmée!</span>
                <span class="english-text">Selection confirmed!</span>
            </h1>
            <p id="headerSubtitle">
                <span class="french-text"><strong>Gardez cette page ouverte pour référence pendant votre dégustation.</strong></span>
                <span class="english-text"><strong>Keep this page open for reference during your tasting.</strong></span>
            </p>
            <div class="instruction-box" id="instructionBox">
                <span class="french-text">SVP rejoignez-nous au comptoir de la boutique pour payer et débuter votre aventure gustative!</span>
                <span class="english-text">Please join us at the store counter to pay and begin your tasting adventure!</span>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loadingState" class="loading">
            <div class="loading-spinner"></div>
            <p>
                <span class="french-text">Chargement de la session...</span>
                <span class="english-text">Loading session...</span>
            </p>
        </div>

        <!-- Main Content -->
        <main class="main-content" id="mainContent" style="display: none;">
            
            <!-- Session Information -->
            <section class="section">
                <!--
                <div class="section-header">
                    <span class="french-text">Informations</span>
                    <span class="english-text">Information</span>
                </div>
                -->
                <div class="section-content">
                    <div class="session-card" id="sessionCard">
                        <!-- Session info will be loaded dynamically -->
                    </div>
                    <div class="guest-list" id="guestList" style="display: none;">
                        <!-- Guests will be loaded dynamically -->
                    </div>
                </div>
            </section>

            <!-- Wine Orders - GROUPED BY GUEST -->
            <section class="section">
                <div class="section-header">
                    <span>
                        <span class="french-text">Sélection par invité</span>
                        <span class="english-text">Selection by guest</span>
                    </span>
                    <span id="wineProgress"></span>
                </div>
                <div class="section-content" id="wineOrders">
                    <!-- Wine orders grouped by guest will be loaded dynamically -->
                </div>
            </section>

            <!-- Plateau Orders -->
            <section class="section" id="plateauSection" style="display: none;">
                <div class="section-header">
                    <span>
                        <span class="french-text">Plateau</span>
                        <span class="english-text">Board</span>
                    </span>
                    <span id="plateauProgress"></span>
                </div>
                <div class="section-content" id="plateauOrders">
                    <!-- Plateau orders will be loaded dynamically -->
                </div>
            </section>

        </main>

        <!-- Email Session Section -->
        <div class="email-section" id="emailSection" style="display: none;">
            <h3>
                <span class="french-text">Envoyez-vous cette dégustation</span>
                <span class="english-text">Email this tasting</span>
            </h3>
                                   
            <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 15px;">
               <button onclick="emailSessionHTML()" class="email-btn">
                    <span class="french-text-white">Entrez votre adresse courriel</span>
                    <span class="english-text-white">Enter your email address</span>
                </button>
            </div>
            
            <p class="email-disclaimer">
                <span class="french-text">Aucune inscription requise</span>
                <span class="english-text">No signup required</span>
            </p>
        </div>

        <!-- Auto-refresh Section -->
        <div class="refresh-section" id="refreshSection" style="display: none;">
            <div class="refresh-info">
                <span class="french-text">Mise à jour automatique:</span> 
                <span class="english-text">Auto-refresh:</span> 
                <span id="countdown">30</span>s
            </div>
            <button class="refresh-btn" id="refreshBtn">
                <span class="french-text">Actualisez</span>
                <span class="english-text">Refresh</span>
            </button>
        </div>
            <!-- Footer -->
        <footer class="footer hide-until-loaded" id="pageFooter">
            <img src="https://jap8t59igsawm5hl.public.blob.vercel-storage.com/ezvino-logo-eml.png" 
                 alt="EZvino" 
                 class="footer-logo">
            <p class="footer-text">
                <span class="french-text">Propulsé par</span>
                <span class="english-text">Powered by</span>
                <a href="https://ezvino.app" class="footer-link">EZvino.app</a>
            </p>
        </footer>
    </div>

    <script>
        // Fix iPhone Safari scroll restoration bug
        if ('scrollRestoration' in history) {
            history.scrollRestoration = 'manual';
        }
        window.scrollTo(0, 0);

        // =============================================
        // Language switching - Complete Version
        // =============================================

        function createLanguageSwitcher() {
            const switchButton = document.createElement('div');
            switchButton.className = 'language-switcher';
            switchButton.innerHTML = `
                <button class="lang-btn" id="fr-btn">FR</button>
                <button class="lang-btn" id="en-btn">EN</button>
            `;
            
            // Append to header instead of body
            const header = document.querySelector('.header-confirmation');
            if (header) {
                header.appendChild(switchButton);
            } else {
                document.body.appendChild(switchButton);
            }
            
            // Add click handlers with localStorage
            document.getElementById('fr-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'fr');
                document.body.setAttribute('data-lang', 'fr');
                document.getElementById('fr-btn').classList.add('active');
                document.getElementById('en-btn').classList.remove('active');
                console.log('French mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };

            document.getElementById('en-btn').onclick = function() {
                localStorage.setItem('selectedLanguage', 'en');
                document.body.setAttribute('data-lang', 'en');
                document.getElementById('en-btn').classList.add('active');
                document.getElementById('fr-btn').classList.remove('active');
                console.log('English mode activated');
                
                // Update dynamic text if function exists
                if (typeof updateGuestCounter === 'function') {
                    updateGuestCounter();
                }
                
                // Update wine checkbox labels for language switch
                if (window.wineTastingApp && typeof window.wineTastingApp.updateGuestLabels === 'function') {
                    window.wineTastingApp.updateGuestLabels();
                }
            };
            
            // Load saved preference or default to French
            const savedLang = localStorage.getItem('selectedLanguage') || 'fr';
            document.getElementById(savedLang + '-btn').click();
        }

        // Run when page loads
        document.addEventListener('DOMContentLoaded', createLanguageSwitcher);

        // =============================================


        // Session Status Page - With Wine Grouping and Email
        class SessionStatusApp {
            constructor() {
                this.API_BASE = '/api';
                this.groupCode = this.extractGroupCode();
                this.sessionData = null;
                this.refreshInterval = null;
                this.countdownInterval = null;
                this.countdown = 30;
                
                this.init();
            }

            extractGroupCode() {
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                console.log('Extracted group code:', code);
                return code;
            }

            async init() {
                if (!this.groupCode) {
                    this.showError('Invalid session URL');
                    return;
                }

                this.setupEventListeners();
                await this.loadSessionData();
                await this.checkEmailFeature();
                this.startAutoRefresh();
            }

            async checkEmailFeature() {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    if (data.emailFeatureEnabled === true) {
                        document.getElementById('emailSection').style.display = 'block';
                    }
                } catch (error) {
                    console.error('Email feature check error:', error);
                }
            }

            setupEventListeners() {
                document.getElementById('refreshBtn').addEventListener('click', () => {
                    this.loadSessionData();
                    this.resetCountdown();
                });

                // Email functionality
                /*
                document.getElementById('sendEmailBtn').addEventListener('click', () => {
                    this.sendEmailRecap();
                });
                */
            }

            async loadSessionData() {
                try {
                    const response = await fetch(`${this.API_BASE}/guest/session/code?code=${this.groupCode}`, {
                        headers: {
                            'X-Winery-Slug': 'Loading winery...'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Session not found');
                    }

                    const data = await response.json();
                    
                    // CRITICAL: Check if auto-refresh should stop
                    if (data.autoRefresh === false) {
                        this.stopAutoRefresh();
                        this.showSessionCompleted(data.session);
                        return;
                    }
                    
                    this.sessionData = data.session; // Use session object directly
                    this.renderSessionData();
                    this.showMainContent();

                } catch (error) {
                    console.error('Error loading session:', error);
                    this.showError(error.message);
                }
            }

            renderSessionData() {
                if (!this.sessionData) return;

                this.renderSessionInfo();
                this.renderGuestList();
                this.renderWineOrdersByGuest();
                this.renderPlateauOrders();
            }

          renderSessionInfo() {
                const sessionCard = document.getElementById('sessionCard');
                const session = this.sessionData;
                
                // Fix timestamp access - try multiple possible property names
                const createdTime = new Date(
                    session.created_at || session.createdat || session.createdAt || Date.now()
                ).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                sessionCard.innerHTML = `
                    <div class="session-group-name">${session.groupName || session.group_name}</div>
                    <div class="session-details">
                        <span class="french-text">Groupe de ${session.guestCount || session.guest_count}</span> 
                        <span class="english-text">Party of ${session.guestCount || session.guest_count}</span>
                    </div>
                    <div class="session-time">
                        <span class="french-text">Créé à </span>
                        <span class="english-text">Created at</span> ${createdTime}
                    </div>
                `;
            }

            renderGuestList() {
                const guestList = document.getElementById('guestList');
                guestList.innerHTML = '';

                this.sessionData.guests.forEach(guest => {
                    const wineCount = guest.wine_orders ? guest.wine_orders.length : 0;
                    const servedCount = guest.wine_orders ? 
                        guest.wine_orders.filter(order => order.status === 'served').length : 0;

                    const guestItem = document.createElement('div');
                    guestItem.className = 'guest-item';
                    
                    guestItem.innerHTML = `
                        <div class="guest-number">${guest.guest_number}</div>
                        <div class="guest-info">
                            <div class="guest-name">${guest.guest_name}</div>
                            <div class="guest-wines">
                                ${servedCount}/${wineCount} 
                                <span class="french-text">vins servis</span>
                                <span class="english-text">wines served</span>
                            </div>
                        </div>
                    `;
                    
                    guestList.appendChild(guestItem);
                });
            }

            renderWineOrdersByGuest() {
                const wineOrders = document.getElementById('wineOrders');
                const wineProgress = document.getElementById('wineProgress');
                wineOrders.innerHTML = '';

                let totalWines = 0;
                let servedWines = 0;

                // Group wines by guest for better readability
                this.sessionData.guests.forEach(guest => {
                    if (guest.wine_orders && guest.wine_orders.length > 0) {
                        const guestCard = document.createElement('div');
                        guestCard.className = 'guest-wine-card';
                        
                        const guestServedCount = guest.wine_orders.filter(wine => wine.status === 'served').length;
                        totalWines += guest.wine_orders.length;
                        servedWines += guestServedCount;

                        guestCard.innerHTML = `
                            <div class="guest-wine-header">
                                <div class="guest-number">${guest.guest_number}</div>
                                <div class="guest-wine-info">
                                    <div class="guest-name">${guest.guest_name}</div>
                                    <div class="guest-wine-count">
                                        ${guestServedCount}/${guest.wine_orders.length} 
                                        <span class="french-text">vins servis</span>
                                        <span class="english-text">wines served</span>
                                    </div>
                                </div>
                            </div>
                            <div class="guest-wines-list">
                                ${guest.wine_orders.map(wine => this.createWineOrderItem(wine)).join('')}
                            </div>
                        `;

                        wineOrders.appendChild(guestCard);
                    }
                });

                wineProgress.textContent = `${servedWines}/${totalWines}`;
            }

            createWineOrderItem(wine) {
                const isServed = wine.status === 'served';
                const statusText = isServed 
                    ? '<span class="french-text">Servi</span><span class="english-text">Served</span>'
                    : '<span class="french-text">Commandé</span><span class="english-text">Ordered</span>';
                const statusClass = isServed ? 'status-served' : 'status-ordered';

                return `
                    <div class="wine-order-item">
                        <div class="wine-item-icon"></div>
                        <div class="wine-item-details">
                            <div class="wine-item-name">${wine.wine_name}</div>
                            <div class="wine-item-category">
                                <span class="french-text">${wine.category_name_fr || wine.wine_category}</span>
                                <span class="english-text">${wine.category_name_en || wine.wine_category}</span>
                            </div>
                        </div>
                        <div class="status-badge ${statusClass}">${statusText}</div>
                    </div>
                `;
            }

            renderPlateauOrders() {
                const plateauContainer = document.getElementById('plateauOrders');
                const plateauSection = document.getElementById('plateauSection');
                const plateauProgress = document.getElementById('plateauProgress');
                
                if (!this.sessionData.plateau_orders || this.sessionData.plateau_orders.length === 0) {
                    plateauSection.style.display = 'none';
                    return;
                }

                plateauSection.style.display = 'block';
                plateauContainer.innerHTML = '';

                let totalPlateaux = 0;
                let servedPlateaux = 0;

                this.sessionData.plateau_orders.forEach(order => {
                    totalPlateaux++;
                    if (order.status === 'served') servedPlateaux++;

                    const isServed = order.status === 'served';
                    const statusText = isServed 
                        ? '<span class="french-text">Servi</span><span class="english-text">Served</span>'
                        : '<span class="french-text">Commandé</span><span class="english-text">Ordered</span>';
                    const statusClass = isServed ? 'status-served' : 'status-ordered';

                    const plateauItem = document.createElement('div');
                    plateauItem.className = 'plateau-item';
                    plateauItem.innerHTML = `
                        <div class="plateau-icon"></div>
                        <div class="plateau-details">
                            <div class="plateau-name french-text">${order.name_fr}</div>
                            <div class="plateau-name english-text">${order.name_en}</div>
                            <div class="plateau-quantity french-text">${order.plateau_count}x pour le groupe</div>
                            <div class="plateau-quantity english-text">${order.plateau_count}x for group</div>
                        </div>
                        <div class="status-badge ${statusClass}">${statusText}</div>
                    `;
                    
                    plateauContainer.appendChild(plateauItem);
                });

                plateauProgress.textContent = `${servedPlateaux}/${totalPlateaux}`;
            }

            async sendEmailRecap() {
                const emailInput = document.getElementById('guestEmail');
                const sendBtn = document.getElementById('sendEmailBtn');
                const email = emailInput.value.trim();

                if (!email || !email.includes('@')) {
                    alert('Veuillez entrer une adresse email valide / Please enter a valid email address');
                    return;
                }

                try {
                    sendBtn.disabled = true;
                    sendBtn.innerHTML = `
                        <span class="french-text">Envoi en cours...</span>
                        <span class="english-text">Sending...</span>
                    `;

                    const response = await fetch(`${this.API_BASE}/guest/email`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Winery-Slug': 'Loading winery'
                        },
                        body: JSON.stringify({
                            group_code: this.groupCode,
                            email: email,
                            session_data: this.sessionData
                        })
                    });

                    if (response.ok) {
                        sendBtn.innerHTML = `
                            ✅ <span class="french-text">Envoyé!</span>
                            <span class="english-text">Sent!</span>
                        `;
                        emailInput.disabled = true;
                        
                        setTimeout(() => {
                            sendBtn.innerHTML = `
                                <span class="french-text">Envoyer le récapitulatif</span>
                                <span class="english-text">Send recap</span>
                            `;
                            sendBtn.disabled = false;
                            emailInput.disabled = false;
                        }, 3000);
                    } else {
                        throw new Error('Failed to send email');
                    }

                } catch (error) {
                    console.error('Error sending email:', error);
                    alert('Erreur lors de l\'envoi / Error sending email');
                    
                    sendBtn.innerHTML = `
                        <span class="french-text">Envoyer le récapitulatif</span>
                        <span class="english-text">Send recap</span>
                    `;
                    sendBtn.disabled = false;
                }
            }

            startAutoRefresh() {
                document.getElementById('refreshSection').style.display = 'block';
                
                this.refreshInterval = setInterval(() => {
                    this.loadSessionData();
                    this.resetCountdown();
                }, 30000);

                this.startCountdown();
            }

            startCountdown() {
                this.countdownInterval = setInterval(() => {
                    this.countdown--;
                    document.getElementById('countdown').textContent = this.countdown;
                    
                    if (this.countdown <= 0) {
                        this.resetCountdown();
                    }
                }, 1000);
            }

            resetCountdown() {
                this.countdown = 30;
                if (document.getElementById('countdown')) {
                    document.getElementById('countdown').textContent = this.countdown;
                }
            }

            showMainContent() {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                document.getElementById('pageFooter').classList.remove('hide-until-loaded');
            }

            showError(message) {
                document.getElementById('loadingState').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #dc2626;">
                        <h3>⚠️ ${message}</h3>
                        <p>
                            <span class="french-text">Veuillez vérifier l'URL de session ou contacter le personnel.</span> 
                            <span class="english-text">Please check your session URL or contact staff.</span>
                        </p>
                        <button onclick="window.history.back()" style="margin-top: 15px; padding: 10px 20px; background: var(--wine-dark); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            <span class="french-text">Retour</span><span class="english-text">Back</span>
                        </button>
                    </div>
                `;
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
                if (this.countdownInterval) {
                    clearInterval(this.countdownInterval);
                    this.countdownInterval = null;
                }
                document.getElementById('refreshSection').style.display = 'none';
                console.log('Auto-refresh stopped - session completed or expired');
            }

            showSessionCompleted(session) {
            // Update header message
            document.getElementById('headerTitle').innerHTML = `
                <span class="french-text">Dégustation terminée!</span>
                <span class="english-text">Tasting completed!</span>
            `;
            
            document.getElementById('headerSubtitle').innerHTML = `
                <span class="french-text"><strong></strong></span>
                <span class="english-text"><strong></strong></span>
            `;
            
            document.getElementById('instructionBox').innerHTML = `
                <span class="french-text">Votre dégustation est maintenant terminée. Au plaisir de vous revoir!</span>
                <span class="english-text">Your tasting is now complete. See you soon!</span>
            `;
            
            // Show ALL session data exactly like active sessions
            this.sessionData = session;
            this.renderSessionData();
            this.showMainContent();
        }
        
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SessionStatusApp();
        });

        // =============================================
        // EMAIL SESSION FUNCTIONALITY
        // =============================================
        
        function emailSession() {
            const isFrench = document.body.getAttribute('data-lang') === 'fr';
            
            const groupName = document.querySelector('.session-group-name')?.textContent?.trim() || 'Group';
            const guestCount = document.querySelector('.session-details')?.textContent?.match(/\d+/)?.[0] || '0';
            // Get session data reference
            const sessionData = window.sessionApp?.sessionData || {};
            
            // Extract all winery info
            const wineryName = sessionData.winery_name || 'Winery';
            const wineryAddress = sessionData.winery_address || '';
            const wineryPhone = sessionData.winery_phone || '';
            const wineryEmail = sessionData.winery_email || '';
            const wineryWebsite = sessionData.winery_website || 'ezvino.app';
            const wineryLogo = sessionData.winery_logo || '';
            
            // Session time
            const sessionTime = new Date(sessionData.created_at).toLocaleTimeString(
                isFrench ? 'fr-CA' : 'en-CA', 
                { hour: '2-digit', minute: '2-digit' }
            );

            const sessionDate = new Date().toLocaleDateString(isFrench ? 'fr-CA' : 'en-CA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            let winesList = '';
            document.querySelectorAll('.guest-wine-card').forEach(guestCard => {
                const guestName = guestCard.querySelector('.guest-name')?.textContent?.trim() || 'Guest';
                const wines = Array.from(guestCard.querySelectorAll('.wine-order-item')).map(item => {
                    const wineName = item.querySelector('.wine-item-name')?.textContent?.trim() || 'Wine';
                    
                    // Get only the visible language category
                    const categoryElement = item.querySelector('.wine-item-category');
                    const wineCategory = isFrench 
                        ? categoryElement?.querySelector('.french-text')?.textContent?.trim()
                        : categoryElement?.querySelector('.english-text')?.textContent?.trim();
                    
                    return `  - ${wineName}${wineCategory ? ' (' + wineCategory + ')' : ''}`;
                });
                
                if (wines.length > 0) {
                    winesList += `${guestName}:\n${wines.join('\n')}\n\n`;
                }
            });
            
            let plateauList = '';
            document.querySelectorAll('.plateau-item').forEach((plateau, index) => {
                const plateauNameFr = plateau.querySelector('.plateau-name.french-text')?.textContent?.trim();
                const plateauNameEn = plateau.querySelector('.plateau-name.english-text')?.textContent?.trim();
                const plateauName = isFrench ? plateauNameFr : plateauNameEn || `Plateau ${index + 1}`;
                const plateauQty = plateau.querySelector('.plateau-quantity')?.textContent?.match(/\d+/)?.[0] || '1';
                plateauList += `${index + 1}. ${plateauName} (${plateauQty}x)\n`;
            });
            
            let subject, body;
            
            if (isFrench) {
                subject = `Dégustation chez ${wineryName} - ${groupName}`;
                body = `Récapitulatif de dégustation

                Date: ${sessionDate}
                Groupe: ${groupName} (${guestCount} personnes)

                Vins dégustés:
                ${winesList || 'Aucun vin'}

                ${plateauList ? `Plateaux:
                ${plateauList}` : ''}

                Merci de votre visite.

                ---
                ezvino.app
                `;
                            } else {
                                subject = `Tasting at ${wineryName} - ${groupName}`;
                                body = `Tasting Recap

                Date: ${sessionDate}
                Group: ${groupName} (${guestCount} people)

                Wines Tasted:
                ${winesList || 'No wines'}

                ${plateauList ? `Boards:
                ${plateauList}` : ''}

                Thank you for your visit.

                ---
                ezvino.app
                `;
            }
            
            const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            window.location.href = mailtoLink;
            
            showEmailNotification(isFrench);
        }
        
        async function emailSessionHTML() {
            const isFrench = document.body.getAttribute('data-lang') === 'fr';
            
            // Prompt for email
            const guestEmail = prompt(
                isFrench 
                    ? 'Entrez votre adresse courriel:' 
                    : 'Enter your email address:'
            );
            
            if (!guestEmail || !guestEmail.includes('@')) {
                alert(isFrench ? 'Adresse courriel invalide' : 'Invalid email address');
                return;
            }

            try {
                const response = await fetch('/api/email/session-recap', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        groupCode: window.sessionApp.groupCode,
                        guestEmail: guestEmail,
                        language: isFrench ? 'fr' : 'en'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    const message = isFrench 
                        ? 'Courriel envoyé avec succès! Vérifiez votre boîte de réception.' 
                        : 'Email sent successfully! Check your inbox.';
                    
                    const notification = document.createElement('div');
                    notification.className = 'email-notification';
                    notification.textContent = message;
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.style.animation = 'slideOut 0.3s ease';
                        setTimeout(() => notification.remove(), 300);
                    }, 5000);
                } else {
                    throw new Error(data.error || 'Failed to send email');
                }

            } catch (error) {
                console.error('Email error:', error);
                alert(isFrench 
                    ? 'Erreur lors de l\'envoi du courriel. Veuillez réessayer.' 
                    : 'Error sending email. Please try again.');
            }
        }

        function showEmailNotification(isFrench) {
            const message = isFrench 
                ? 'Votre application de courriel devrait s\'ouvrir' 
                : 'Your email app should open';
            
            const notification = document.createElement('div');
            notification.className = 'email-notification';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.sessionApp = new SessionStatusApp();
        });
    </script>
</body>
</html>