/* =============================================
 * COMPLETE THEME SYSTEM - FULL CONTROL
 * All visual elements centralized
 * ============================================= */

:root {
  /* Brand Colors */
  --brand-primary: #8b0000;
  --brand-secondary: #A52A2A;
  --wine-gold: #d4af37;
  --wine-cream: #f8f9fa;
  --wine-dark: #8b0000;
  --success-green: #D4EDDA;
  
  /* Text Colors - Fixed Readability */
  --text-french: #000000;
  --text-english: #000000;  /* Darker - readable on burgundy */
  --text-light: #666666;
  --text-white: #ffffff;
  
 /* French/English Text Classes */
        .french-text {
            color: var(--text-french);
        }

        .english-text {
            color: var(--text-english);
        }

         .french-text-white {
            color: var(--wine-cream);
        }

        .english-text-white {
            color: var(--wine-cream);
        }

  /* Remove Gradients */
  --header-bg: var(--brand-primary);  /* Solid color instead of gradient */
  --section-header-bg: var(--wine-cream);
}

.header {
            background: var(--brand-primary) ;
            color: white;
            padding: 20px;
            position: relative;
            text-align: left;
            overflow: hidden;
        }

.header-confirmation {
            background: #d4edda ;
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="wine-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23wine-pattern)"/></svg>') repeat;
    opacity: 0.3;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--wine-cream) 0%, #f8f6f0 100%);
    color: var(--wine-cream);
    line-height: 1.6;
    min-height: 100vh;
    padding: 0;
    /* FOUC Prevention  visibility: */
}

/* FOUC Prevention 
body.loaded {
    visibility: visible; /* Show after CSS loads 
}*/

.winery-logo {
  width: 60px;
  height: 60px;
  background: none;
  border: none;
  border-radius: 0;
  font-size: 16px;
  font-weight: bold;
  color: transparent !important;
  text-align: center;
}

/* Let JavaScript control logo visibility */
.winery-logo[style*="display: none"] {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.winery-logo img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px; /* Keep circular or change to 8px for rounded square */
}

.winery-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    position: relative;
    z-index: 1;
    color: var(--wine-cream);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    color: var(--wine-cream);
    font-size: 14px;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}


/* =============================================
 * Buttons
 * ============================================= */

/* Language Switcher Styles - Header Positioned */
.language-switcher {
    display: flex;
    gap: 3px;
    z-index: 1000;
    /* REMOVED: position: absolute, top, right - let flexbox handle it */
}

/* Ensure header supports absolute positioning */
.header {
    position: relative;
}

.lang-btn {
    background: rgba(255,255,255,0.8);
    color: #333;
    border: 1px solid rgba(0,0,0,0.2);
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: normal;
    transition: all 0.2s ease;
}

.lang-btn.active {
    background: rgba(255,255,255,1);
    font-weight: bold;
    color: #000;
}

/* Language visibility rules - all text variants */
body[data-lang="fr"] .english-text,
body[data-lang="fr"] .english-text-white { 
    display: none !important; 
}

body[data-lang="en"] .french-text,
body[data-lang="en"] .french-text-white { 
    display: none !important; 
}


.remove-guest-btn {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-guest-btn {
            background: white;
            color: #000000;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .add-guest-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

         /* Auto-refresh Section */
        .refresh-section {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .refresh-info {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 10px;
        }

        .refresh-btn {
            background: white;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(114, 47, 55, 0.3);
        }

        /* Submit Button */
        .submit-section {
            padding: 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
            position: sticky;
            bottom: 0;
            z-index: 100;
        }

        .submit-btn {
            width: 100%;
            background: #D4EDDA;
            color: white;
            border: none;
            padding: 18px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px #D4EDDA;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px #D4EDDA;
        }

        .submit-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .hard-delete-btn { 
            background: #8b0000; 
            color: white; 
        }
        .hard-delete-btn:hover { 
            background: #6d0000; 
        }


/* =============================================
 * Content Styles
 * ============================================= */

 .closed-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.40);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 900;
}

.closed-message {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 350px;
}

.retry-btn {
    margin-top: 20px;
    padding: 12px 24px;
    background: var(--brand-primary);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Main Content */
        .main-content {
            padding: 20px;
        }

        .section {
            margin-bottom: 30px;
            background: var(--wine-cream);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: var(--wine-cream);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
            position: relative;
        }

        .section-content {
            padding: 20px;
        }

/* =============================================
 * Email Section
 * ============================================= */
        
        .email-section {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .email-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .email-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .email-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .email-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--wine-dark);
            margin-bottom: 8px;
        }

        .email-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }

        .email-form {
            margin-top: 20px;
        }

        .email-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .email-input:focus {
            outline: none;
            border-color: var(--wine-dark);
            box-shadow: 0 0 0 3px rgba(114, 47, 55, 0.1);
        }

        .email-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--wine-gold) 0%, #b8860b 100%);
            color: var(--wine-dark);
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .email-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
        }

        .email-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }


/* =============================================
 * WINE Section - Guest Index
 * ============================================= */


        /* Wine Selection */
        .wine-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .wine-card {
            padding: 15px;
            background: white;
            transition: all 0.3s ease;
            border-radius: 12px;
            border-left: 6px solid var(--wine-dark)!important; /* <-- set your color here */
        }

        .wine-card:hover {
            border-color: var(--wine-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .wine-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .wine-image {
            width: 50px;
            height: 60px;
            background: var(--wine-dark);
            border-radius: 6px;
            margin-right: 15px;
            display: none !important;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .wine-image::before {
            content: '🍷';
            position: absolute;
            font-size: 24px;
        }

        .wine-item {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .wine-info {
            flex: 1;
        }

        .wine-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--wine-dark);
            margin-bottom: 5px;
        }

        .wine-category {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .wine-description {
            margin-top: 10px;
            font-size: 14px;
            font-style: italic;
            color: #374151;
            line-height: 1.5;
            margin-bottom: 15px;
        }

       /* PNG Icon Integration - ADD THIS TO YOUR GUEST PAGE */
        .wine-glass-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 10px;
            display: block;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .wine-glass-active {
            background-image: url('../shared/assets/wine-full.png');
        }

        .wine-glass-empty {
            background-image: url('../shared/assets/wine-empty.png');
        }

        /* Plateau Selection */
        .plateau-card {
            border: 2px solid #f3f4f6;
            border-radius: 12px;
            padding: 15px;
            background: white;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .plateau-card:hover {
            border-color: var(--wine-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .plateau-header {
            display: block;
            margin-bottom: 10px;
        }

        .plateau-imageNOTUSED {
            width: 50px;
            height: 50px;
            background: transparent;
            border-radius: 8px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            display: none !important;
        }

        .plateau-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
        }

        .plateau-icon {
            width: 45px;
            height: 45px;
            border-radius: 10px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: transparent !important;
        }

        /* Guest Wine Cards */
        .guest-wine-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 15px;
            overflow: hidden;
            padding: 15px;
            transition: all 0.3s ease;
            border-left: 6px solid var(--wine-dark)!important; /* <-- set your color here */
        }

        .guest-wine-header {
            background: var(--wine-cream);
            padding: 15px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
        }

        .guest-number {
            background: var(--wine-dark);
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            font-size: 16px;
        }

        .guest-wine-info {
            flex: 1;
            color: #000000 !important;
            text-transform: uppercase;
            margin-left: 0;
            font-size: 20px;
            font-weight: bold;
        }

        .guest-wine-count {
            font-size: 12px;
            color: #64748b;
            margin-top: 2px;
            visibility: hidden !important;
        }

/* Guest Wine Selection */
        .guest-wine-selection {
            border-top: 1px solid #e5e7eb;
            padding-top: 15px;
        }

        .guest-selection-header {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--wine-dark);
        }

        .guest-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .guest-checkbox {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #000000;
        }

        .guest-checkbox:hover {
            background: #f3f4f6;
            border-color: var(--wine-dark);
        }

        .guest-checkbox.selected {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-color: var(--wine-dark);
        }

        .guest-checkbox input[type="checkbox"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            accent-color: var(--wine-dark);
        }

        .guest-checkbox label {
            font-size: 12px;
            cursor: pointer;
            flex: 1;
        }

        .wine-selection-counter {
            margin-left: auto;
            font-size: 10px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 10px;
        }

        /* Default: Side by side for guest session */
        .wine-order-item,
        .plateau-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #f1f5f9;
            gap: 10px;
        }

        /* Host session: Stack on mobile */
        .host-session .wine-order-item,
        .host-session .plateau-item {
            flex-direction: column;
        }

        .host-session .serving-toggle {
            margin-left: 0;
        }

        /* Host session desktop: Side by side */
        @media (min-width: 768px) {
            .host-session .wine-order-item,
            .host-session .plateau-item {
                flex-direction: row;
            }
            
            .host-session .serving-toggle {
                margin-left: auto;
            }
        }

        .wine-order-item:last-child {
            border-bottom: none;
        }

        .wine-item-details
        .plateau-details {
            flex: 1;
        }

        .wine-item-name {
            font-weight: 600;
            color: var(--wine-dark);
            font-size: 14px;
            margin-bottom: 2px;
        }

        .wine-item-category {
            font-size: 11px;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }


        /* Plateau Items */

        .plateau-item:last-child {
            border-bottom: none;
        }

        .plateau-name {
            font-weight: 600;
            color: var(--wine-dark);
            margin-bottom: 4px;
            font-size: 15px;
        }

        .plateau-quantity {
            font-size: 12px;
            color: #64748b;
        }

        .quantity-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 2px solid var(--wine-dark);
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: var(--wine-dark);
            color: white;
        }

        .quantity-display {
            font-size: 18px;
            font-weight: bold;
            color: var(--wine-dark);
            min-width: 30px;
            text-align: center;
        }

    .plateau-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 28px;
            height: 28px;
            background-image: url('../shared/assets/plateau.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }


/* =============================================
   LAYOUT UTILITIES
   ============================================= */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-lg {
  max-width: 1400px;
}

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

/* =============================================
   RESPONSIVE DESIGN
   ============================================= */

/* Mobile First - Base styles above are mobile */

/* Tablet */
@media (min-width: 768px) {
  :root {
    --text-xs: 0.8rem;
    --text-sm: 0.9rem;
    --text-base: 1rem;
    --text-lg: 1.2rem;
    --text-xl: 1.4rem;
    --text-2xl: 1.6rem;
    --text-3xl: 2rem;
    --text-4xl: 2.5rem;
  }

  .container {
    padding: 0 var(--space-6);
  }

  .grid-tablet-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }

  .grid-desktop-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-desktop-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* =============================================
   HOST DASHBOARD - VISUAL SPEED SERVICE
   ============================================= */

.service-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;
}

.service-icon {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--wine-deep-red) 0%, var(--wine-dark) 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-details {
  flex: 1;
}

.service-type {
  font-size: 18px;
  font-weight: 700;
  color: var(--wine-dark);
  margin-bottom: 8px;
}

.service-progress {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #059669 0%, #047857 100%); /* GREEN instead of yellow */
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 1px 3px rgba(5, 150, 105, 0.3);
}





/* =============================================
   DASHBOARD CARDS - ICON CENTERED ON CARD
   ============================================= */

.service-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;
}

.service-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--wine-deep-red) 0%, var(--wine-dark) 100%);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-icon img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-details {
  width: 100%;
}

.service-progress {
  font-size: 24px;
  color: var(--wine-dark);
  margin-bottom: 8px;
  font-weight: 700;
}



/* Hide lock status and NEW badge on dashboard cards */
.session-tile .lock-indicator,
.session-tile .status-badge {
  display: none;
}

 /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .dashboard-title {
            color: var(--wine-dark);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 30px;
            text-align: center;
        }

        /* Session Grid */
        .sessions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        /* Session Tile */
        .session-tile {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .session-tile:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            border-color: var(--wine-dark);
        }

        .session-tile.new {
            border-left: 6px solid var(--success-green);
        }

        .session-tile.in-progress {
            border-left: 6px solid var(--wine-gold);
        }

        .session-tile.completed {
            border-left: 6px solid var(--wine-dark);
            opacity: 0.8;
        }

        /* Tile Header */
        .tile-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .group-info h3 {
            font-size: 22px;
            font-weight: 600;
            color: var(--wine-dark);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .group-meta {
            display: flex;
            gap: 15px;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .party-size {
            background: var(--wine-dark);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 16px;
        }

        .time-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--wine-dark);
            opacity: 0.7;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 15px;
        }

        .empty-state p {
            font-size: 16px;
            opacity: 0.9;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--wine-dark);
            opacity: 0.7;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(114, 47, 55, 0.2);
            border-top: 4px solid var(--wine-dark);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

/* =============================================
   MOUSE DRAG TOGGLES - LAPTOP COMPATIBLE
   ============================================= */

/* SERVING TOGGLE - WORKING VERSION */
.serving-toggle {
    position: relative;
    width: 170px;
    height: 36px;
    background: #ba6767;
    border: 2px solid #ba6767;
    border-radius: 18px;
    align-self: center; /* Center the toggle button */
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

.serving-toggle.served {
    background: var(--success-green);
    border-color: #D4EDDA;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 30px;
    height: 30px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.serving-toggle.served .toggle-slider {
    left: 134px;
}

.toggle-label {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    font-weight: 600;
    color: #000000;
    left: 40px;
    transition: all 0.3s ease;
}

.serving-toggle.served .toggle-label {
    color: white;
    left: 8px;
}

.header-confirmation {
    background: #d4edda;
    color: white;
    padding: 20px;
    text-align: center;
    position: relative; /* CRITICAL - allows absolute positioning of language switcher */
    overflow: hidden;
}

/* Language switcher for confirmation header - absolute positioned */
.header-confirmation .language-switcher {
    position: absolute;
    top: 15px;
    right: 20px;
}

/* =============================================
 * GUEST INDEX HEADER ARCHITECTURE
 * ============================================= */

.header-content {
    position: relative;
}

.header-row-1 {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;
}

.header-row-2 {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    justify-content: flex-start;
}

.header-row-3 {
    text-align: center;
}

/* Mobile responsive for all headers */
@media (max-width: 768px) {
    .header {
        text-align: left !important;
    }
    
    .language-switcher {
        position: static;
    }
    
    .header-row-2 {
        justify-content: flex-start;
    }
}

/* Desktop - side by side */
@media (min-width: 768px) {
    .wine-order-item
    .plateau-item {
        flex-direction: row;
    }
    
    .wine-item-details 
    .plateau-details {
        flex: 1;
    }
    
    .serving-toggle {
        align-self: auto;
    }
}

.dashboard-stats {
            display: flex;
            gap: 30px;
            align-items: center;
            justify-content: right;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            display: block;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .stat-label {
            font-size: 14px;
            color: rgba(255,255,255,0.8);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .refresh-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }

        .refresh-dot {
            width: 8px;
            height: 8px;
            background: #008800;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

/* =============================================
   PRINT STYLES
   ============================================= */

@media print {
  .btn,
  .toggle,
  .no-print {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}