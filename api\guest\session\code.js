// api/guest/session/code.js - COMPLETE REPLACEMENT
// Stops auto-refresh for completed/expired sessions

import { query } from '../../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { code } = req.query;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                error: 'Session code is required'
            });
        }

        // Get session with status and timing info
        const sessionQuery = `
            SELECT 
                ts.*,
                EXTRACT(EPOCH FROM (NOW() - ts.created_at))/3600 as hours_since_created,
                CASE 
                  WHEN ts.status = 'completed' THEN 'completed'
                  WHEN EXTRACT(EPOCH FROM (NOW() - ts.created_at))/3600 > 4 THEN 'expired'
                  WHEN (
                      SELECT COUNT(*) 
                      FROM wine_orders wo 
                      WHERE wo.session_id = ts.id AND wo.status != 'served'
                  ) = 0 AND (
                      SELECT COUNT(*) 
                      FROM plateau_orders po 
                      WHERE po.session_id = ts.id AND po.status != 'served'
                  ) = 0 THEN 'completed'
                  ELSE 'active'
              END as computed_status
            FROM tasting_sessions ts 
            WHERE ts.group_code = $1
        `;
        
        const sessionResult = await query(sessionQuery, [code]);
        
        if (!sessionResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: 'Session not found'
            });
        }

        const session = sessionResult.rows[0];
        
        // CRITICAL: For completed/expired sessions, get full data but stop auto-refresh
        if (session.computed_status !== 'active') {
            // Get complete session data same as active sessions
            const guestsQuery = `
                SELECT g.*, 
                      array_agg(
                          json_build_object(
                                'wine_id', wo.wine_id,
                                'wine_name', w.name,
                                'wine_category', w.category,
                                'category_name_fr', wc.name_fr,
                                'category_name_en', wc.name_en,
                                'status', wo.status,
                                'served_at', wo.served_at
                            ) ORDER BY wo.id
                      ) as wine_orders
                FROM guests g
                LEFT JOIN wine_orders wo ON g.id = wo.guest_id
                LEFT JOIN wines w ON wo.wine_id = w.id
                LEFT JOIN wine_categories wc ON w.category = wc.category_code
                WHERE g.session_id = $1
                GROUP BY g.id
                ORDER BY g.guest_number
            `;
            
            const guestsResult = await query(guestsQuery, [session.id]);
            
            const plateauOrdersQuery = `
                SELECT po.*, p.name_fr, p.name_en
                FROM plateau_orders po
                LEFT JOIN plateaux p ON po.plateau_id = p.id
                WHERE po.session_id = $1
                ORDER BY po.id
            `;
            
            const plateauOrdersResult = await query(plateauOrdersQuery, [session.id]);

            return res.status(200).json({
                success: true,
                session: {
                    ...session,
                    status: session.computed_status,
                    guests: guestsResult.rows,
                    plateau_orders: plateauOrdersResult.rows
                },
                autoRefresh: false // STOP AUTO-REFRESH but keep all data
            });
        }

        // Only get detailed data for active sessions
        const guestsQuery = `
            SELECT g.*, 
                   array_agg(
                       json_build_object(
                            'wine_id', wo.wine_id,
                            'wine_name', w.name,
                            'wine_category', w.category,
                            'category_name_fr', wc.name_fr,
                            'category_name_en', wc.name_en,
                            'status', wo.status,
                            'served_at', wo.served_at
                        ) ORDER BY wo.id
                   ) as wine_orders
            FROM guests g
            LEFT JOIN wine_orders wo ON g.id = wo.guest_id
            LEFT JOIN wines w ON wo.wine_id = w.id
            LEFT JOIN wine_categories wc ON w.category = wc.category_code
            WHERE g.session_id = $1
            GROUP BY g.id
            ORDER BY g.guest_number
        `;
        
        const guestsResult = await query(guestsQuery, [session.id]);
        
        const plateauOrdersQuery = `
            SELECT po.*, p.name_fr, p.name_en
            FROM plateau_orders po
            LEFT JOIN plateaux p ON po.plateau_id = p.id
            WHERE po.session_id = $1
            ORDER BY po.id
        `;
        
        const plateauOrdersResult = await query(plateauOrdersQuery, [session.id]);

        // Get winery data for branding
        const wineryQuery = 'SELECT name, logo, subscription_tier FROM wineries WHERE id = $1';
        const wineryResult = await query(wineryQuery, [session.winery_id]);
        const winery = wineryResult.rows[0];

        res.status(200).json({
            success: true,
            session: {
                ...session,
                status: session.computed_status,
                guests: guestsResult.rows,
                plateau_orders: plateauOrdersResult.rows
            },
            winery: winery, // Add winery branding data
            autoRefresh: true
        });

    } catch (error) {
        console.error('Session status error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch session status'
        });
    }
}