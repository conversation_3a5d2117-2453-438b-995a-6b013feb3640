import { put } from '@vercel/blob';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { filename } = req.query;
    
    // Upload to Vercel Blob (req is the file stream)
    const blob = await put(filename || `logo-${Date.now()}.png`, req, {
      access: 'public',
      allowOverwrite: true  // This allows overwriting existing files
    });
    
    res.status(200).json(blob); // Return the blob object directly
    
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      error: 'Upload failed',
      details: error.message 
    });
  }
}