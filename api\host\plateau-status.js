// api/host/plateau-status.js - FIXED with direct subdomain detection
import { query } from '../../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'PATCH') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        const { plateau_order_id, status, served_by } = req.body;
        
        if (!plateau_order_id || !status) {
            return res.status(400).json({
                success: false,
                error: 'plateau_order_id and status are required'
            });
        }

        if (!['ordered', 'served', 'cancelled'].includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid status. Must be: ordered, served, or cancelled'
            });
        }

        // Direct subdomain extraction (consistent with working APIs)
        const host = req.headers.host || '';
        const winerySlug = host.split('.')[0];
        
        if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
            return res.status(400).json({
                success: false,
                error: 'Invalid winery subdomain'
            });
        }

        // Get winery by slug
        const wineryResult = await query(
            'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
            [winerySlug]
        );

        if (!wineryResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: `Winery "${winerySlug}" not found`
            });
        }

        const winery = wineryResult.rows[0];

        // Verify the plateau order belongs to this winery's session
        const verifyQuery = `
            SELECT po.id, po.session_id, ts.winery_id, p.name_fr, p.name_en
            FROM plateau_orders po
            JOIN tasting_sessions ts ON po.session_id = ts.id
            LEFT JOIN plateaux p ON po.plateau_id = p.id
            WHERE po.id = $1 AND ts.winery_id = $2
        `;

        const verifyResult = await query(verifyQuery, [plateau_order_id, winery.id]);

        if (verifyResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Plateau order not found for this winery'
            });
        }

        // Update plateau order status
        const updateQuery = `
            UPDATE plateau_orders 
            SET status = $1,
                served_at = $2,
                served_by = $3
            WHERE id = $4
            RETURNING *
        `;

        const updateValues = [
            status,
            status === 'served' ? new Date() : null,
            status === 'served' ? (served_by || 'Host') : null,
            plateau_order_id
        ];

        const updateResult = await query(updateQuery, updateValues);

        res.status(200).json({
            success: true,
            message: `Plateau order status updated to ${status}`,
            plateau_order: updateResult.rows[0],
            plateau_name: `${verifyResult.rows[0].name_fr} / ${verifyResult.rows[0].name_en}`
        });

    } catch (error) {
        console.error('Plateau status update error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update plateau status'
        });
    }
}