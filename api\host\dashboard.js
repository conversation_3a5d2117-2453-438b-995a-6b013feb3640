// api/host/dashboard.js - COMPLETE CLEAN VERSION

import { query } from '../../lib/database.js';

// Operating hours utility
// Operating hours utility with debug logging
function isWineryOpen(winery, extensionHours = 2) {
  try {
    const timezone = winery.timezone || 'UTC';
    const now = new Date();
    
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      weekday: 'long',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(now);
    const currentDay = parts.find(part => part.type === 'weekday').value.toLowerCase();
    const currentTime = `${parts.find(part => part.type === 'hour').value}:${parts.find(part => part.type === 'minute').value}`;
    
    const dayHours = winery.operational_hours?.[currentDay];
    
    if (!dayHours || dayHours.closed) {
      console.log(`HOURS DEBUG: ${winery.name} - Closed today (${currentDay})`);
      return { isOpen: false, currentDay, currentTime, reason: 'Closed today' };
    }
    
    const currentMinutes = timeToMinutes(currentTime);
    const openMinutes = timeToMinutes(dayHours.open);
    const closeMinutes = timeToMinutes(dayHours.close);
    
    // Calculate listening window
    const listenEndMinutes = closeMinutes + (extensionHours * 60);
    
    const isOpen = currentMinutes >= openMinutes && currentMinutes <= listenEndMinutes;
    
    // Debug logging
    const formatMinutes = (min) => `${Math.floor(min/60).toString().padStart(2,'0')}:${(min%60).toString().padStart(2,'0')}`;
    console.log(`HOURS DEBUG: ${winery.name} on ${currentDay}`);
    console.log(`  Current time: ${currentTime} (${currentMinutes} min)`);
    console.log(`  Store hours: ${dayHours.open} - ${dayHours.close}`);
    console.log(`  Listening window: ${dayHours.open} - ${formatMinutes(listenEndMinutes)}`);
    console.log(`  Result: ${isOpen ? 'ONLINE' : 'OFFLINE'}`);
    
    return { 
      isOpen, 
      currentDay, 
      currentTime, 
      reason: isOpen ? 'Open' : 'Outside listening window',
      debug: {
        listenStart: formatMinutes(openMinutes),
        listenEnd: formatMinutes(listenEndMinutes),
        storeOpen: dayHours.open,
        storeClose: dayHours.close
      }
    };
    
  } catch (error) {
    console.log(`HOURS DEBUG: Error checking hours - ${error.message}`);
    return { isOpen: true, reason: 'Unable to determine - defaulting open' };
  }
}

function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        // Direct subdomain extraction (consistent with working pattern)
        const host = req.headers.host || '';
        const winerySlug = host.split('.')[0];
        
        console.log('Dashboard API - Host:', host);
        console.log('Dashboard API - Winery Slug:', winerySlug);
        
        if (!winerySlug || winerySlug === 'ezvino' || winerySlug === 'www') {
            return res.status(400).json({
                success: false,
                error: 'Invalid winery subdomain'
            });
        }

        // Look up winery by slug
        const wineryResult = await query(
            'SELECT * FROM wineries WHERE slug = $1 AND is_active = true',
            [winerySlug]
        );

        if (!wineryResult.rows.length) {
            return res.status(404).json({
                success: false,
                error: `Winery "${winerySlug}" not found or unavailable`
            });
        }

        const winery = wineryResult.rows[0];

        // Check operating hours
        const hoursCheck = isWineryOpen(winery, 2);

        if (!hoursCheck.isOpen) {
            return res.status(200).json({
                success: true,
                isOpen: false,
                winery: { 
                    id: winery.id,
                    name: winery.name,
                    slug: winery.slug,
                    subscription_tier: winery.subscription_tier,
                    logo: winery.logo
                },
                summary: {
                    totalActiveSessions: 0,
                    totalActiveGuests: 0,
                    sessionsInProgress: 0,
                    completedSessions: 0
                },
                sessions: []
            });
        }

        // STEP 1: Auto-complete sessions older than 4 hours
            const timezone = winery.timezone || 'America/New_York';
           
            // SINGLE QUERY - Get everything we need without timezone complexity
            const allDataQuery = `
                WITH session_data AS (
                    SELECT 
                        ts.id as session_id,
                        ts.group_code,
                        ts.group_name,
                        ts.guest_count,
                        ts.status,
                        ts.created_at,
                        sl.is_locked,
                        CASE
                            WHEN (COUNT(wo.id) = 0 AND COUNT(DISTINCT po.id) = 0) THEN 'new'
                            WHEN (COUNT(wo.id) = COUNT(CASE WHEN wo.status = 'served' THEN 1 END))
                                AND (COUNT(DISTINCT po.id) = 0 OR COUNT(DISTINCT po.id) = COUNT(DISTINCT CASE WHEN po.status = 'served' THEN po.id END))
                            THEN 'completed'
                            ELSE 'in_progress'
                        END as overall_status,
                        COUNT(wo.id) as wine_count,
                        COUNT(CASE WHEN wo.status = 'served' THEN 1 END) as wine_served,
                        COUNT(DISTINCT po.id) as plateau_count,
                        COUNT(DISTINCT CASE WHEN po.status = 'served' THEN po.id END) as plateau_served,
                        COALESCE(SUM(po.plateau_count), 0) as plateau_quantity
                    FROM tasting_sessions ts
                    LEFT JOIN session_locks sl ON ts.id = sl.session_id
                    LEFT JOIN wine_orders wo ON ts.id = wo.session_id
                    LEFT JOIN plateau_orders po ON ts.id = po.session_id
                    WHERE ts.winery_id = $1 
                    AND ts.status IN ('active', 'completed')
                    AND DATE(ts.created_at - INTERVAL '4 hours') = DATE(CURRENT_TIMESTAMP - INTERVAL '4 hours')
                    GROUP BY ts.id, ts.group_code, ts.group_name, ts.guest_count, ts.status, ts.created_at, sl.is_locked
                )
                SELECT 
                    session_id, group_code, group_name, guest_count, status, created_at, is_locked,
                    overall_status, wine_count, wine_served, plateau_count, plateau_served, plateau_quantity,
                    (SELECT COUNT(*) FROM session_data) as total_sessions,
                    (SELECT SUM(guest_count) FROM session_data) as total_guests,
                    (SELECT COALESCE(SUM(po.plateau_count), 0) FROM plateau_orders po 
                    JOIN tasting_sessions ts ON po.session_id = ts.id 
                    WHERE ts.winery_id = $1 
                    AND ts.status IN ('active', 'completed')
                    AND DATE(ts.created_at - INTERVAL '4 hours') = DATE(CURRENT_TIMESTAMP - INTERVAL '4 hours')) as total_plateaux,
                    (SELECT COUNT(*) FROM session_data WHERE overall_status = 'in_progress') as sessions_in_progress
                FROM session_data
                ORDER BY created_at DESC
            `;

            const result = await query(allDataQuery, [winery.id]);
            const rows = result.rows;

            // Extract totals from first row (same for all rows)
            const totals = rows.length > 0 ? rows[0] : {
                total_sessions: 0,
                total_guests: 0,
                total_plateaux: 0,
                sessions_in_progress: 0
            };

            // Build sessions array
            const sessions = rows.map(row => ({
                sessionId: row.session_id,
                groupCode: row.group_code,
                groupName: row.group_name,
                guestCount: row.guest_count,
                status: row.status,
                createdAt: row.created_at,
                isLocked: row.is_locked || false,
                overallStatus: row.overall_status,
                iconFile: row.overall_status === 'completed' ? 'wine-empty.png' : 
                        (row.plateau_count > 0 ? 'plateau.png' : 'wine-full.png'),
                wineOrders: {
                    total: parseInt(row.wine_count || 0),
                    served: parseInt(row.wine_served || 0),
                    progress: row.wine_count > 0 ? Math.round((row.wine_served / row.wine_count) * 100) : 0
                },
                plateauOrders: {
                    total: parseInt(row.plateau_count || 0),
                    served: parseInt(row.plateau_served || 0),
                    progress: row.plateau_count > 0 ? Math.round((row.plateau_served / row.plateau_count) * 100) : 0
                }
            }));

            // Sort completed sessions last
            sessions.sort((a, b) => {
                if (a.overallStatus === 'completed' && b.overallStatus !== 'completed') return 1;
                if (b.overallStatus === 'completed' && a.overallStatus !== 'completed') return -1;
                return new Date(a.createdAt) - new Date(b.createdAt);
            });

            const summary = {
                totalActiveSessions: parseInt(totals.total_sessions || 0),
                totalActiveGuests: parseInt(totals.total_guests || 0),
                sessionsInProgress: parseInt(totals.sessions_in_progress || 0),
                plateauxServed: parseInt(totals.total_plateaux || 0),
                completedSessions: sessions.filter(s => s.overallStatus === 'completed').length
            };

        res.status(200).json({
            success: true,
            isOpen: true,
            winery: {
                id: winery.id,
                name: winery.name,
                slug: winery.slug,
                subscription_tier: winery.subscription_tier,
                logo: winery.logo
            },
            summary: summary,
            sessions: sessions,
            metadata: {
                date: new Date().toLocaleDateString('en-US', { timeZone: timezone }),
                totalSessions: sessions.length
            }
        });

    } catch (error) {
        console.error('Host dashboard error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to load dashboard'
        });
    }
}