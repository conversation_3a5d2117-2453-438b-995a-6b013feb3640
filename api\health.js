import { query } from '../lib/database.js';

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // Check for migration request FIRST
    if (req.query.migrate === 'categories') {
        try {
            await query(`
                CREATE TABLE IF NOT EXISTS wine_categories (
                    id SERIAL PRIMARY KEY,
                    category_code VARCHAR(50) UNIQUE NOT NULL,
                    name_fr VARCHAR(100) NOT NULL,
                    name_en VARCHAR(100) NOT NULL,
                    sort_order INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
            
            // Insert categories
            const categories = [
                ['red', 'Vin Rouge', 'Red Wine', 1],
                ['white', 'Vin Blanc', 'White Wine', 2],
                ['rose', 'Vin Rosé', 'Rosé', 3],
                ['sparkling', '<PERSON> Mo<PERSON>', 'Sparkling', 4],
                ['dessert', 'Vin de Dessert', 'Dessert Wine', 5],
                ['cider', 'Cidre', 'Cider', 6],
                ['orange', 'Vin Orange', 'Orange Wine', 7],
                ['natural_red', 'Vin Nature Rouge', 'Natural Red', 8],
                ['natural_white', 'Vin Nature Blanc', 'Natural White', 9],
                ['natural_rose', 'Vin Nature Rosé', 'Natural Rosé', 10],
                ['fortified', 'Vin Fortifié', 'Fortified Wine', 11],
                ['ice_wine', 'Vin de Glace', 'Ice Wine', 12],
                ['pet_nat', 'Pétillant Naturel', 'Pet-Nat', 13]
            ];
            
            for (const [code, name_fr, name_en, sort_order] of categories) {
                await query(`
                    INSERT INTO wine_categories (category_code, name_fr, name_en, sort_order)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (category_code) DO NOTHING
                `, [code, name_fr, name_en, sort_order]);
            }
            
            return res.status(200).json({
                status: 'migration_complete',
                message: 'Categories table created and populated',
                categories_added: categories.length
            });
        } catch (error) {
            return res.status(500).json({ error: error.message });
        }
    }

    // Normal health check
    try {
        res.status(200).json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            emailFeatureEnabled: process.env.EMAIL_FEATURE_ENABLED === 'true'
        });

    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
    
}