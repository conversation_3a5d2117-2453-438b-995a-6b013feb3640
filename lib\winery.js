// lib/winery.js - UPDATED
import { query } from './database.js';

export const extractWineryFromRequest = (req) => {
    let winerySlug = req.headers['x-winery-slug'];
    
    if (!winerySlug) {
        const hostname = req.headers.host || '';
        winerySlug = hostname.split('.')[0];
        
        // Better fallback for development and Vercel URLs
        if (!winerySlug || 
            winerySlug === 'localhost' || 
            winerySlug === '127' || 
            winerySlug.includes('localhost') ||
            winerySlug.includes('vercel') ||
            winerySlug.includes('wine-tasting-serverless')) {
            winerySlug = 'chateau-test';
        }
    }
    
    return {
        slug: winerySlug,
        isValid: winerySlug && winerySlug.length > 0
    };
};

export const getWineryData = async (slug) => {
    const queryText = 'SELECT * FROM wineries WHERE slug = $1 AND is_active = true';
    const result = await query(queryText, [slug]);
    
    if (!result.rows.length) {
        throw new Error(`Winery '${slug}' not found`);
    }
    
    return result.rows[0];
};